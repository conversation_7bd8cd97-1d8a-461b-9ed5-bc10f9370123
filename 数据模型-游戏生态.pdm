<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ID="{02BCA7EF-DEB9-4F57-87A7-E844542D5832}" Label="" LastModificationDate="**********" Name="1-数据模型：活动中台" Objects="589" Symbols="68" Target="MySQL 5.0" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="16.5.0.3982"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>02BCA7EF-DEB9-4F57-87A7-E844542D5832</a:ObjectID>
<a:Name>1-数据模型：活动中台</a:Name>
<a:Code>1-数据模型：活动中台</a:Code>
<a:CreationDate>1372327097</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591928802</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=tqdj_crebas.sql
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=D:\
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=No
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No
Grant=Yes

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=No
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=No
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=No
Foreign key=No
Alternate key=No
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
EstimationYears=0
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes

[FolderOptions\CheckModel]

[FolderOptions\CheckModel\Package]

[FolderOptions\CheckModel\Package\CircularReference]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\ConstraintName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CnstMaxLen]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CircularDependency]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\ShortcutUniqCode]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Package\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table]

[FolderOptions\CheckModel\Table\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqIndex]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - COLNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - INDXCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - KEYCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\SerialColumnNumber]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyCollYesYes]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\TableIndexes]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Table\CheckTablePartitionKey]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableStartDate]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MYSQL50_Table_Table_storage_type]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableRefNoLifecycle]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableSourceMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTablePartialColumnMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableKeyColumnMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableNotOnLifecycleTablespace]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column]

[FolderOptions\CheckModel\Table.Column\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DomainDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColumnMandatory]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckNumParam]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckPrecSupLng]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\FkeyDttpDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\FkeyCheckDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColnSqncNoKey]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColnSqncDttp]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\SerialColumnFK]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColumnCompExpr]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MYSQL50_Column_Auto_increment_key]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MYSQL50_Column_Datatype_attributes]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnOneToOneMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnDataTypeMapping]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnNoMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckDttpIncompatibleFormat]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index]

[FolderOptions\CheckModel\Table.Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\EmptyColl - CIDXCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\UndefIndexType]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\IndexColumnCount]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\IQIndxHNGUniq]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\MYSQL50_Index_Fulltext_indexes_validity]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key]

[FolderOptions\CheckModel\Table.Key\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\EmptyColl - COLNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\MultiKeySqnc]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger]

[FolderOptions\CheckModel\Table.Trigger\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index]

[FolderOptions\CheckModel\Join Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View]

[FolderOptions\CheckModel\View\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\View\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index]

[FolderOptions\CheckModel\View.View Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\EmptyColl - CIDXCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\IndexColumnCount]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference]

[FolderOptions\CheckModel\Reference\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\Reflexive]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\EmptyColl - RFJNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\IncompleteJoin]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\JoinOrder]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference]

[FolderOptions\CheckModel\View Reference\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\EmptyColl - VRFJNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain]

[FolderOptions\CheckModel\Domain\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckNumParam]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckPrecSupLng]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckDttpIncompatibleFormat]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default]

[FolderOptions\CheckModel\Default\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DfltValeEmpty]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DfltSameVale]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User]

[FolderOptions\CheckModel\User\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniquePassword]
CheckSeverity=No
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\User\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group]

[FolderOptions\CheckModel\Group\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\EmptyColl - USERCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniquePassword]
CheckSeverity=No
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Group\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role]

[FolderOptions\CheckModel\Role\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\EmptyColl - USERCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure]

[FolderOptions\CheckModel\Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\ProcBodyEmpty]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger]

[FolderOptions\CheckModel\DBMS Trigger\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DbmsTriggerEvent]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source]

[FolderOptions\CheckModel\Data Source\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\EmptyColl - MODLSRC]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DtscTargets]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\CheckDataSourceModels]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning]

[FolderOptions\CheckModel\Horizontal Partitioning\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\EmptyColl - PARTCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning]

[FolderOptions\CheckModel\Vertical Partitioning\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\EmptyColl - PARTCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing]

[FolderOptions\CheckModel\Table Collapsing\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\EmptyColl - TargetTable]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube]

[FolderOptions\CheckModel\Cube\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\EmptyColl - ALLOLINKCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\EmptyColl - Facts]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\CubeDupAssociation]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Cube\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact]

[FolderOptions\CheckModel\Fact\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\EmptyColl - MEASCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension]

[FolderOptions\CheckModel\Dimension\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\EmptyColl - DATTRCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\EmptyColl - HIERCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DimnDupHierarchy]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DimnDefHierarchy]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\SerialColumnNumber]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association]

[FolderOptions\CheckModel\Association\EmptyColl - Hierarchy]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute]

[FolderOptions\CheckModel\Dimension.Attribute\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure]

[FolderOptions\CheckModel\Fact.Measure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy]

[FolderOptions\CheckModel\Dimension.Hierarchy\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\EmptyColl - DATTRCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym]

[FolderOptions\CheckModel\Synonym\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\EmptyColl - BASEOBJ]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type]

[FolderOptions\CheckModel\Abstract Data Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\AdtInstantiable]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\AdtAbstractUsed]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure]

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\AdtProcUniqName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package]

[FolderOptions\CheckModel\Database Package\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\EmptyColl - PROCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\EmptyColl - CURCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - VARCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - TYPCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - EXCCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Procedure]

[FolderOptions\CheckModel\Database Package.Package Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Procedure\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Package Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence]

[FolderOptions\CheckModel\Sequence\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Cursor]

[FolderOptions\CheckModel\Database Package.Package Cursor\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Cursor\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Cursor\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Cursor\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Cursor\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Package Variable]

[FolderOptions\CheckModel\Database Package.Package Variable\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Variable\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Variable\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Type]

[FolderOptions\CheckModel\Database Package.Package Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Type\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Exception]

[FolderOptions\CheckModel\Database Package.Package Exception\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Package Exception\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace]

[FolderOptions\CheckModel\Tablespace\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage]

[FolderOptions\CheckModel\Storage\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database]

[FolderOptions\CheckModel\Database\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service]

[FolderOptions\CheckModel\Web Service\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation]

[FolderOptions\CheckModel\Web Service.Web Operation\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle]

[FolderOptions\CheckModel\Lifecycle\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckLifecyclePhase]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckLifecycleRetention]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckPartitionRange]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase]

[FolderOptions\CheckModel\Lifecycle.Phase\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseTbspace]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseIQTbspace]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseDuplicateTbspace]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseTbspaceCurrency]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseRetention]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseIdlePeriod]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseDataSource]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseExternalOnFirst]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Replication]

[FolderOptions\CheckModel\Replication\PartialReplication]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule]

[FolderOptions\CheckModel\Business Rule\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\EmptyColl - OBJCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object]

[FolderOptions\CheckModel\Extended Object\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link]

[FolderOptions\CheckModel\Extended Link\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File]

[FolderOptions\CheckModel\File\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\CheckPathExists]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure]

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor]

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Variable]

[FolderOptions\CheckModel\Database Package.Database Package Variable\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type]

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception]

[FolderOptions\CheckModel\Database Package.Database Package Exception\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format]

[FolderOptions\CheckModel\Data Format\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\CheckDataFormatNullExpression]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
UseTerm=No
EnableRequirements=No
EnableFullShortcut=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=1
RefrUpdateConstraint=1
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Xsm]
GenRootElement=Yes
GenComplexType=No
GenAttribute=Yes
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>4A179406-08C1-4619-875F-D0968C6128B0</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1372327097</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1372327097</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>74258565-9F26-405C-8F95-1742510A5DD1</a:ObjectID>
<a:Name>01-抽奖任务</a:Name>
<a:Code>01-抽奖任务</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Show Links intersections=No
Activate automatic link routing=No
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Show Icon=No
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;#$%&amp;&#39;)*+,-./:;=&gt;?@\]^_`|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject.TextStyle=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.TextStyle=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Package.Stereotype=Yes
Package.Comment=No
Package.IconPicture=No
Package.TextStyle=No
Package_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table.TextStyle=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;\&amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;\&amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View.TextStyle=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure.TextStyle=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:RectangleSymbol Id="o5">
<a:CreationDate>1589860634</a:CreationDate>
<a:ModificationDate>1591196175</a:ModificationDate>
<a:Rect>((-53424,20171), (-33546,-22907))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16711680</a:LineColor>
<a:FillColor>********</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontName>新宋体,8,N</a:FontName>
<a:ManuallyResized>1</a:ManuallyResized>
</o:RectangleSymbol>
<o:ExtendedDependencySymbol Id="o6">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-23334,-21598), (-22734,2557))</a:Rect>
<a:ListOfPoints>((-23039,-21598),(-23039,-2567),(-23034,-2567),(-23034,2557))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<a:AutomaticRoutingState>4</a:AutomaticRoutingState>
<c:SourceSymbol>
<o:TableSymbol Ref="o7"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o8"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o9"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o10">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-19300,-11122), (-3157,-10522))</a:Rect>
<a:ListOfPoints>((-3157,-10841),(-12714,-10841),(-12714,-10822),(-19300,-10822))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<a:AutomaticRoutingState>4</a:AutomaticRoutingState>
<c:SourceSymbol>
<o:TableSymbol Ref="o11"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o7"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o12"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o13">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-3605,-7038), (-3005,1923))</a:Rect>
<a:ListOfPoints>((-3305,-7038),(-3305,1923))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o11"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o14"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o15"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ReferenceSymbol Id="o16">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-25105,22000), (1809,22450))</a:Rect>
<a:ListOfPoints>((1809,22225),(-25105,22225))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:AutomaticRoutingState>4</a:AutomaticRoutingState>
<c:SourceSymbol>
<o:TableSymbol Ref="o17"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o18"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o19"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o20">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-4584,7595), (-4134,21643))</a:Rect>
<a:ListOfPoints>((-4359,7595),(-4359,21643))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o14"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o17"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o21"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o22">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-19886,8052), (-7489,8502))</a:Rect>
<a:ListOfPoints>((-7489,8277),(-19886,8277))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o14"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o8"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o23"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o24">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-63476,6702), (-43178,7152))</a:Rect>
<a:ListOfPoints>((-43178,6943),(-55749,6943),(-55749,6927),(-63476,6927))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:AutomaticRoutingState>4</a:AutomaticRoutingState>
<c:SourceSymbol>
<o:TableSymbol Ref="o25"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o26"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o27"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o28">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-38261,9680), (-28906,10130))</a:Rect>
<a:ListOfPoints>((-38261,9905),(-28906,9905))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o25"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o8"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o29"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o30">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-63071,-1366), (-40278,-916))</a:Rect>
<a:ListOfPoints>((-40278,-1141),(-63071,-1141))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:AutomaticRoutingState>4</a:AutomaticRoutingState>
<c:SourceSymbol>
<o:TableSymbol Ref="o31"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o26"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o32"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o33">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-41130,-6908), (-29177,3752))</a:Rect>
<a:ListOfPoints>((-41130,-6908),(-29402,-6908),(-29402,3752))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:AutomaticRoutingState>2</a:AutomaticRoutingState>
<c:SourceSymbol>
<o:TableSymbol Ref="o31"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o8"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o34"/>
</c:Object>
</o:ReferenceSymbol>
<o:TableSymbol Id="o8">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-31606,1845), (-18122,13125))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>********</a:LineColor>
<a:FillColor>255</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o35"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o25">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-51222,2621), (-35968,15715))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>********</a:LineColor>
<a:FillColor>16744703</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o36"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o7">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>1591589034</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-28225,-21908), (-16773,-6978))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>********</a:LineColor>
<a:FillColor>8421504</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o37"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o14">
<a:CreationDate>1589858092</a:CreationDate>
<a:ModificationDate>1591185051</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-10880,-1142), (3832,13818))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>********</a:LineColor>
<a:FillColor>16776960</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o38"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o26">
<a:CreationDate>1589858633</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-71580,-1752), (-60276,8206))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>********</a:LineColor>
<a:FillColor>16711808</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o39"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o11">
<a:CreationDate>1589859804</a:CreationDate>
<a:ModificationDate>1591589061</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-8655,-21907), (3531,-6101))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>********</a:LineColor>
<a:FillColor>8421504</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o40"/>
</c:Object>
</o:TableSymbol>
<o:TextSymbol Id="o41">
<a:Text>{\rtf1\ansi\ansicpg936\deff0\deflang1033\deflangfe2052{\fonttbl{\f0\fmodern\fprq6\fcharset134 \&#39;cb\&#39;ce\&#39;cc\&#39;e5;}{\f1\fnil\fcharset0 Microsoft Sans Serif;}}
{\*\generator Msftedit 5.41.21.2510;}\viewkind4\uc1\pard\lang2052\f0\fs20\&#39;b3\&#39;e9\&#39;bd\&#39;b1\&#39;c4\&#39;a3\&#39;d0\&#39;cd\lang1033\f1\par
}
</a:Text>
<a:CreationDate>1589860654</a:CreationDate>
<a:ModificationDate>1591109561</a:ModificationDate>
<a:Rect>((-51517,16621), (-46717,20221))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>********</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontName>新宋体,8,N</a:FontName>
</o:TextSymbol>
<o:TableSymbol Id="o18">
<a:CreationDate>1590201745</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-27882,18377), (-16578,27929))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>********</a:LineColor>
<a:FillColor>65280</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o42"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o31">
<a:CreationDate>1591109184</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-51247,-13406), (-36489,-150))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>********</a:LineColor>
<a:FillColor>8454143</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o43"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o17">
<a:CreationDate>1591175569</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-10411,18050), (4301,27370))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>********</a:LineColor>
<a:FillColor>16776960</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o44"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o45">
<a:CreationDate>1591187744</a:CreationDate>
<a:ModificationDate>1591669025</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-49122,-21592), (-38170,-14968))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>********</a:LineColor>
<a:FillColor>8421504</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o46"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
<o:PhysicalDiagram Id="o47">
<a:ObjectID>88F923C3-1D6D-47F5-B28C-4B653E3BE714</a:ObjectID>
<a:Name>09-系统支持</a:Name>
<a:Code>09-系统支持</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Show Links intersections=No
Activate automatic link routing=No
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Show Icon=No
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;#$%&amp;&#39;)*+,-./:;=&gt;?@\]^_`|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject.TextStyle=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.TextStyle=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Package.Stereotype=Yes
Package.Comment=No
Package.IconPicture=No
Package.TextStyle=No
Package_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table.TextStyle=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;\&amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;\&amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View.TextStyle=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure.TextStyle=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:RectangleSymbol Id="o48">
<a:Text>系统支持</a:Text>
<a:CreationDate>1511774762</a:CreationDate>
<a:ModificationDate>1591928833</a:ModificationDate>
<a:Rect>((-36638,-8062), (16464,22214))</a:Rect>
<a:FontColor>255</a:FontColor>
<a:TextStyle>4114</a:TextStyle>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16711680</a:LineColor>
<a:FillColor>********</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontName>新宋体,10,N</a:FontName>
<a:ManuallyResized>1</a:ManuallyResized>
</o:RectangleSymbol>
<o:TableSymbol Id="o49">
<a:CreationDate>1511774762</a:CreationDate>
<a:ModificationDate>1591928808</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-554,7761), (10748,16609))</a:Rect>
<a:LineColor>********</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<c:Object>
<o:Table Ref="o50"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o51">
<a:CreationDate>1511774762</a:CreationDate>
<a:ModificationDate>1591928805</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-16709,9624), (-5793,16822))</a:Rect>
<a:LineColor>********</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<c:Object>
<o:Table Ref="o52"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o53">
<a:CreationDate>1511774762</a:CreationDate>
<a:ModificationDate>1512808361</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-33564,2549), (-21490,17173))</a:Rect>
<a:LineColor>********</a:LineColor>
<a:FillColor>33023</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>********</a:GradientEndColor>
<c:Object>
<o:Table Ref="o54"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o50">
<a:ObjectID>8E76E213-EFE8-44A2-BEF2-121D01BC3D16</a:ObjectID>
<a:Name>系统参数</a:Name>
<a:Code>ge_parameter</a:Code>
<a:CreationDate>1373086952</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591928824</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>name、clazz，value 3个字段值的组合必须唯一 </a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o55">
<a:ObjectID>336E9BF3-EE36-41AC-AEED-7539CA30E2A8</a:ObjectID>
<a:Name>参数标识</a:Name>
<a:Code>pid</a:Code>
<a:CreationDate>1373086987</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>int</a:DataType>
<a:Identity>1</a:Identity>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o56">
<a:ObjectID>F2654E30-D858-4433-9522-1D4E0E22EF86</a:ObjectID>
<a:Name>参数名字</a:Name>
<a:Code>name</a:Code>
<a:CreationDate>1373086987</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>参数的名字</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o57">
<a:ObjectID>6A74D4AF-CB8D-4876-AB42-E86396959E02</a:ObjectID>
<a:Name>参数分类</a:Name>
<a:Code>clazz</a:Code>
<a:CreationDate>1373086987</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o58">
<a:ObjectID>5D292226-A638-4004-B3AD-2325FBF25179</a:ObjectID>
<a:Name>参数取值</a:Name>
<a:Code>value</a:Code>
<a:CreationDate>1373086987</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>参数的一个取值（参数可取其值域内的任何一个值）</a:Comment>
<a:DataType>varchar(2048)</a:DataType>
<a:Length>2048</a:Length>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>6E409BF0-11FB-4AC0-A93D-28724C90E48F</a:ObjectID>
<a:Name>取值别名</a:Name>
<a:Code>alias</a:Code>
<a:CreationDate>1373086987</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>为 VALUE 值取的一个能表达取值意含的名字，若本字段为空，显示处理上应让  ALIAS = VALUE</a:Comment>
<a:DataType>varchar(256)</a:DataType>
<a:Length>256</a:Length>
</o:Column>
<o:Column Id="o60">
<a:ObjectID>475BB604-7B12-4D83-B45F-602D30052CB3</a:ObjectID>
<a:Name>显示顺序</a:Name>
<a:Code>shword</a:Code>
<a:CreationDate>1373086987</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>列表时， 在无其它排序优先级情况下， 值小者排前。</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>20B80103-BFFC-4ACF-AD82-5906FE6BC01B</a:ObjectID>
<a:Name>取值说明</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1373086987</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>对参数取值意含的更详细说明</a:Comment>
<a:DataType>varchar(512)</a:DataType>
<a:Length>512</a:Length>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>CABDB188-C647-415E-9BB6-4D3F33F64B0C</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>created</a:Code>
<a:CreationDate>1373086987</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o63">
<a:ObjectID>AA3AAC3F-DDA2-4A24-907D-7D5F9684D948</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>modified</a:Code>
<a:CreationDate>1373086987</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o64">
<a:ObjectID>5C2157F6-FA36-4FD5-BA2D-1DBD71BB98A7</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1373087707</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o55"/>
</c:Key.Columns>
</o:Key>
<o:Key Id="o65">
<a:ObjectID>56D3ED27-4CCB-46BD-B63B-B24C6F6E9FC7</a:ObjectID>
<a:Name>Key_2</a:Name>
<a:Code>Key_2</a:Code>
<a:CreationDate>1373089658</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,57={D1C795B8-8F7B-4AA7-A240-85C5B737C543},ExtUnique,4=true

</a:ExtendedAttributesText>
<c:Key.Columns>
<o:Column Ref="o56"/>
<o:Column Ref="o57"/>
<o:Column Ref="o58"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o64"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o52">
<a:ObjectID>7169C33D-2B88-46F5-84CD-8BF51AED7C02</a:ObjectID>
<a:Name>标识生成器</a:Name>
<a:Code>ge_id_generator</a:Code>
<a:CreationDate>1373087014</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591928820</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o66">
<a:ObjectID>622A572D-48C5-46F2-8727-55E3AF2CA6EF</a:ObjectID>
<a:Name>生成器名</a:Name>
<a:Code>name</a:Code>
<a:CreationDate>1373087043</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>9BAB3132-F824-4D91-B1F8-0A461EE0F9BB</a:ObjectID>
<a:Name>当前标识</a:Name>
<a:Code>curr</a:Code>
<a:CreationDate>1373087043</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>生成器当前已经用到的值，在这个值之前的ID值已被分配出去而不可用，只能使用其后面的值</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>A4F6B180-0E7B-4E02-8666-B8193C8BBC9E</a:ObjectID>
<a:Name>分配块长</a:Name>
<a:Code>count</a:Code>
<a:CreationDate>1373087043</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>每次分配的个数</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>BDB87D72-E92E-4C2B-9549-8E8F2882C3A3</a:ObjectID>
<a:Name>详细说明</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1373087043</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(512)</a:DataType>
<a:Length>512</a:Length>
</o:Column>
<o:Column Id="o70">
<a:ObjectID>DD7576B1-CCFF-4CAA-A917-7CC259E96918</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>created</a:Code>
<a:CreationDate>1373087043</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o71">
<a:ObjectID>10D82DCD-4E19-4AC1-8390-57B36B493FF5</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>modified</a:Code>
<a:CreationDate>1373087043</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o72">
<a:ObjectID>C0369B3E-14DD-46FC-A1AC-932325A90FC0</a:ObjectID>
<a:Name>数据版本</a:Name>
<a:Code>datver</a:Code>
<a:CreationDate>1373202826</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o73">
<a:ObjectID>3B8D0AA8-69AD-443E-B432-8248A7C95133</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1373087795</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o66"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o73"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o54">
<a:ObjectID>C8A855B1-C0CF-4C29-8DF6-F684C8DE0288</a:ObjectID>
<a:Name>业务数据配置</a:Name>
<a:Code>ge_busiconf</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591928815</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>业务数据配置表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o74">
<a:ObjectID>AA911785-755B-4EC9-9336-7DC6DC30D2DD</a:ObjectID>
<a:Name>配置标识</a:Name>
<a:Code>bcid</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>配置标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Identity>1</a:Identity>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o75">
<a:ObjectID>85023A9D-89F5-400F-912C-63DA50350B9E</a:ObjectID>
<a:Name>业务编码</a:Name>
<a:Code>business</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>业务编码</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>5978063D-E9DA-434A-AAA1-1730F07677A8</a:ObjectID>
<a:Name>业务模块</a:Name>
<a:Code>module</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>业务模块</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>712462A8-AA0C-48DB-9ADF-C1692C0DD761</a:ObjectID>
<a:Name>配置项名</a:Name>
<a:Code>name</a:Code>
<a:CreationDate>1401411806</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>配置名称</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o78">
<a:ObjectID>12C7E4E2-3E7F-4F9A-AEE8-DD24E539B1FC</a:ObjectID>
<a:Name>配置内容</a:Name>
<a:Code>content</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>配置内容，足够大，若不够可用上数据字段1~数据字段5</a:Comment>
<a:DataType>varchar(30000)</a:DataType>
<a:Length>30000</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o79">
<a:ObjectID>9A3763E0-1EEC-43A3-958C-6D8978F6EE34</a:ObjectID>
<a:Name>数据字段1</a:Name>
<a:Code>data1</a:Code>
<a:CreationDate>1401412075</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(3000)</a:DataType>
<a:Length>3000</a:Length>
</o:Column>
<o:Column Id="o80">
<a:ObjectID>0C358AEB-891A-4422-8782-9FC19C9F8648</a:ObjectID>
<a:Name>备用字段2</a:Name>
<a:Code>data2</a:Code>
<a:CreationDate>1401411806</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(3000)</a:DataType>
<a:Length>3000</a:Length>
</o:Column>
<o:Column Id="o81">
<a:ObjectID>54675AE4-9307-4E67-A365-3578D6F534E3</a:ObjectID>
<a:Name>备用字段3</a:Name>
<a:Code>data3</a:Code>
<a:CreationDate>1401412075</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(3000)</a:DataType>
<a:Length>3000</a:Length>
</o:Column>
<o:Column Id="o82">
<a:ObjectID>253B362A-83C9-47E4-BEA9-40407E3898B8</a:ObjectID>
<a:Name>备用字段4</a:Name>
<a:Code>data4</a:Code>
<a:CreationDate>1401412075</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(3000)</a:DataType>
<a:Length>3000</a:Length>
</o:Column>
<o:Column Id="o83">
<a:ObjectID>F68885AD-D4C1-4009-BE7E-BD35F99BBDB0</a:ObjectID>
<a:Name>备用字段5</a:Name>
<a:Code>data5</a:Code>
<a:CreationDate>1401412075</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(3000)</a:DataType>
<a:Length>3000</a:Length>
</o:Column>
<o:Column Id="o84">
<a:ObjectID>4B604841-7D68-4321-ABA0-64E1A13849E1</a:ObjectID>
<a:Name>配置说明</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>为 VALUE 值取的一个能表达取值意含的名字，若本字段为空，显示处理上应让  ALIAS = VALUE</a:Comment>
<a:DataType>varchar(1000)</a:DataType>
<a:Length>1000</a:Length>
</o:Column>
<o:Column Id="o85">
<a:ObjectID>6D11C074-3E68-4ABD-A6EA-934A2F11D824</a:ObjectID>
<a:Name>显示顺序</a:Name>
<a:Code>shword</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1401411749</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>列表时， 在无其它排序优先级情况下， 值小者排前。</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>F9B9A28B-9A90-4725-8A47-C38A29BD187A</a:ObjectID>
<a:Name>创建 人</a:Name>
<a:Code>creator</a:Code>
<a:CreationDate>1401411806</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>AD8D07F1-DE9F-49FA-873C-61376285C96B</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>created</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>6879064F-022B-42EC-8BB6-9998FDA3E5D4</a:ObjectID>
<a:Name>修改人</a:Name>
<a:Code>modifier</a:Code>
<a:CreationDate>1401411806</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>95E3C81E-14B6-47F6-B678-741EC2EA7A39</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>modified</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o90">
<a:ObjectID>6E21975B-D4EE-4B99-8B86-49ABA89975F0</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1401411749</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o74"/>
</c:Key.Columns>
</o:Key>
<o:Key Id="o91">
<a:ObjectID>E46EFBBE-FACC-49DC-A1D5-CFC57A17B831</a:ObjectID>
<a:Name>Key_2</a:Name>
<a:Code>Key_2</a:Code>
<a:CreationDate>1401411749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1511774762</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,57={D1C795B8-8F7B-4AA7-A240-85C5B737C543},ExtUnique,4=true

</a:ExtendedAttributesText>
<c:Key.Columns>
<o:Column Ref="o75"/>
<o:Column Ref="o76"/>
<o:Column Ref="o77"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o90"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o35">
<a:ObjectID>323A7AC0-9120-49CA-A922-00309F0BE64C</a:ObjectID>
<a:Name>抽奖包</a:Name>
<a:Code>ge_lottery_package</a:Code>
<a:CreationDate>1406015220</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186828</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>抽奖包</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o92">
<a:ObjectID>1FE59FD3-8DE0-44E1-81CC-890837A1C7CA</a:ObjectID>
<a:Name>奖包标识</a:Name>
<a:Code>package_id</a:Code>
<a:CreationDate>1406015227</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590201090</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o93">
<a:ObjectID>A81A0DE3-8947-46E4-9C3E-055E0024CE83</a:ObjectID>
<a:Name>奖包类型</a:Name>
<a:Code>package_type</a:Code>
<a:CreationDate>1590115360</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590487960</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包类型, 0-谢谢参与， 1-有实际内容需要做发放的奖包</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o94">
<a:ObjectID>6B3A1752-B116-4A5F-B981-AAB50D470845</a:ObjectID>
<a:Name>奖包名称</a:Name>
<a:Code>package_name</a:Code>
<a:CreationDate>1406016425</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590149264</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包名称</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o95">
<a:ObjectID>1BEE619A-321B-4AE4-B0ED-DA364E6F9FC4</a:ObjectID>
<a:Name>奖包图片</a:Name>
<a:Code>package_image</a:Code>
<a:CreationDate>1406016425</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590116651</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包图片</a:Comment>
<a:DataType>varchar(1024)</a:DataType>
<a:Length>1024</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>50CDBE8A-5DA3-4DDA-AD20-CC5AB03FA912</a:ObjectID>
<a:Name>选中时图片</a:Name>
<a:Code>choice_image</a:Code>
<a:CreationDate>1406016515</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590116614</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>选中时图片</a:Comment>
<a:DataType>varchar(1024)</a:DataType>
<a:Length>1024</a:Length>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>B5FD3B9C-242A-4536-9962-8FF6CAFFBB8E</a:ObjectID>
<a:Name>鼠标移上tips</a:Name>
<a:Code>mouseover_tips</a:Code>
<a:CreationDate>1406016515</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590116627</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>鼠标移上去时候的tips</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>C7EFBD7F-39FE-4FDE-BDA1-F6D178E2EB4E</a:ObjectID>
<a:Name>点跳地址</a:Name>
<a:Code>skip_url</a:Code>
<a:CreationDate>1406016515</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590116668</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>点击跳转地址</a:Comment>
<a:DataType>varchar(1024)</a:DataType>
<a:Length>1024</a:Length>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>003621E0-7C73-494A-9C46-8B5AF470B6E2</a:ObjectID>
<a:Name>说明信息</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590112553</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>说明信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o100">
<a:ObjectID>E3031732-CFD5-4F8D-94B7-8408FE9AABBE</a:ObjectID>
<a:Name>扩展信息</a:Name>
<a:Code>extjson</a:Code>
<a:CreationDate>1374314851</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590056058</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>扩展信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o101">
<a:ObjectID>820FEDD4-AB62-4035-862C-D6507D2B3B90</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>ctime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117380</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o102">
<a:ObjectID>97D264E1-C954-4972-A2F9-5572A4DEBBAD</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>utime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117380</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o103">
<a:ObjectID>42945DB6-D135-4006-95E7-24A08C321BCB</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1406015220</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o92"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o103"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o36">
<a:ObjectID>6165A9E3-EA71-4671-9A54-1535A55E9F35</a:ObjectID>
<a:Name>抽奖模型1</a:Name>
<a:Code>ge_lottery_model1</a:Code>
<a:CreationDate>1406016637</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591194144</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>抽奖模型1</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o104">
<a:ObjectID>FD958F99-F8FB-48E1-8F6C-69B00B2F802E</a:ObjectID>
<a:Name>任务标识</a:Name>
<a:Code>task_id</a:Code>
<a:CreationDate>1406016637</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590055872</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>任务标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>301C3692-9DFD-4EE5-828F-283D55741951</a:ObjectID>
<a:Name>奖包标识</a:Name>
<a:Code>package_id</a:Code>
<a:CreationDate>1406016637</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590112395</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>5B3F3DC3-A66E-47E2-9F76-2E161B68D4FF</a:ObjectID>
<a:Name>显示位置</a:Name>
<a:Code>position</a:Code>
<a:CreationDate>1407314835</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591168510</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>显示位置，值小排前</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>08509CB4-E11E-44AE-A7FA-B531A623D375</a:ObjectID>
<a:Name>中奖概率</a:Name>
<a:Code>probability</a:Code>
<a:CreationDate>1406017863</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590116196</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>中奖概率</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o108">
<a:ObjectID>842AD53C-5C15-467C-AAB4-19A65467FE02</a:ObjectID>
<a:Name>可抽总份数</a:Name>
<a:Code>package_total</a:Code>
<a:CreationDate>1590289750</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591101994</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>可抽总上限, 小于 0 则不限制</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>5E5D2490-56C9-4A87-A219-FBE893A5A249</a:ObjectID>
<a:Name>用户抽中上限</a:Name>
<a:Code>user_hit_limit</a:Code>
<a:CreationDate>1406017623</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591189550</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>用户抽中上限, 小于 0 则不限制</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>B52A7726-A0BC-4952-B7C0-7303C60DECB5</a:ObjectID>
<a:Name>每日抽中上限</a:Name>
<a:Code>daily_hit_limit</a:Code>
<a:CreationDate>1590496363</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591189534</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>每日抽中上限，小于0无限制</a:Comment>
<a:DefaultValue>-1</a:DefaultValue>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>8241FFBC-001F-4082-80C2-D47DA59B4217</a:ObjectID>
<a:Name>日限额统计分组</a:Name>
<a:Code>daily_limit_group</a:Code>
<a:CreationDate>1591194012</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591234818</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>日限额统计分组，若空则在 package_id 上统计</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>DC9B40E6-175B-47BE-862D-10C186E32284</a:ObjectID>
<a:Name>已抽走数量</a:Name>
<a:Code>consumed</a:Code>
<a:CreationDate>1406023168</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590290014</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>已抽走数量</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>55559381-C338-4B71-8EC8-FDE44F644AFB</a:ObjectID>
<a:Name>项目状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1372327548</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590292468</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>控制任务的状态，0 无效，1有效</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o114">
<a:ObjectID>DAD64070-77B7-4459-8C3F-612E159D7562</a:ObjectID>
<a:Name>说明信息</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590068007</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>说明信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>23AAC715-F4D0-4852-A6A4-38BD309B6646</a:ObjectID>
<a:Name>扩展信息</a:Name>
<a:Code>extjson</a:Code>
<a:CreationDate>1374314851</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590056000</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>扩展信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o116">
<a:ObjectID>979E97D1-1EA5-4DD0-ABAD-46C295937293</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>ctime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117367</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o117">
<a:ObjectID>B54E931D-4094-4698-9008-A459F118C32D</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>utime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117367</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o118">
<a:ObjectID>F6C1E49B-193F-47B4-A466-42C18F182434</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1406016637</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590053707</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o104"/>
<o:Column Ref="o105"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o118"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o37">
<a:ObjectID>14745F69-5052-4645-8639-6159B0E51369</a:ObjectID>
<a:Name>中奖记录</a:Name>
<a:Code>ge_lottery_record</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591588975</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>中奖记录</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o119">
<a:ObjectID>5FB8DFEA-25A4-4F66-A6D6-8C16E68D5D28</a:ObjectID>
<a:Name>中奖标识</a:Name>
<a:Code>record_id</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591494436</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>中奖标识</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>4A5448CB-93A7-4F4A-A269-34682EB3FA79</a:ObjectID>
<a:Name>请求序号</a:Name>
<a:Code>seq</a:Code>
<a:CreationDate>1591443499</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591443755</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>请求序号，用来唯一标识一个请求，防止重复</a:Comment>
<a:DataType>char(48)</a:DataType>
<a:Length>48</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o121">
<a:ObjectID>BFE82D9B-E5DA-493E-8DD2-EC8E983F9C96</a:ObjectID>
<a:Name>任务标识</a:Name>
<a:Code>task_id</a:Code>
<a:CreationDate>1406022143</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590115780</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>任务标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>E1341D40-4FAF-494C-8C5B-987228333053</a:ObjectID>
<a:Name>奖包标识</a:Name>
<a:Code>package_id</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590113383</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>9EB2EE86-A863-4E9B-8DA7-18B48077962C</a:ObjectID>
<a:Name>奖包名称</a:Name>
<a:Code>package_name</a:Code>
<a:CreationDate>1406016425</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590149228</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包名称</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>FF2A5055-EE39-4952-BFF7-839D067DB6F5</a:ObjectID>
<a:Name>中奖人UID</a:Name>
<a:Code>uid</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590113781</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>中奖人UID</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>E562C1A3-486A-4440-874B-0D7A490FC23F</a:ObjectID>
<a:Name>收货信息</a:Name>
<a:Code>address</a:Code>
<a:CreationDate>1406020685</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117097</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>收货信息</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>86811794-DD07-492D-A7A4-B843ABD0335A</a:ObjectID>
<a:Name>发放状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1406022435</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590317763</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>发放状态: 0-未发, 1-已发,  -1-发失败</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o127">
<a:ObjectID>EB888597-AC21-492B-8085-53901F2DB2D0</a:ObjectID>
<a:Name>抽奖人IP</a:Name>
<a:Code>ip</a:Code>
<a:CreationDate>1590064436</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590113594</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>抽奖人IP</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o128">
<a:ObjectID>35AF8EC5-991A-4B2B-8F5D-101AC0B0293A</a:ObjectID>
<a:Name>抽奖人MAC</a:Name>
<a:Code>mac</a:Code>
<a:CreationDate>1590064436</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590113594</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>抽奖人MAC</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o129">
<a:ObjectID>FFBAE4B1-8202-4430-AE5F-7D2BB2C0B305</a:ObjectID>
<a:Name>额外长整数</a:Name>
<a:Code>extlong</a:Code>
<a:CreationDate>1591588832</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591589097</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>额外长整数，放一些额外信息如辅助统计的值</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o130">
<a:ObjectID>3A4A4D94-E77C-4A45-932A-7D409A7D2F95</a:ObjectID>
<a:Name>额外字符串</a:Name>
<a:Code>exstr</a:Code>
<a:CreationDate>1591588832</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591589008</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>额外字符串，放一些额外信息如辅助统计的值</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o131">
<a:ObjectID>2CA52C20-6D67-465F-8679-C15D13F44BFE</a:ObjectID>
<a:Name>扩展信息</a:Name>
<a:Code>extjson</a:Code>
<a:CreationDate>1374314851</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590246212</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>扩展信息</a:Comment>
<a:DataType>varchar(512)</a:DataType>
<a:Length>512</a:Length>
</o:Column>
<o:Column Id="o132">
<a:ObjectID>427CA7F2-2A3C-4D26-949C-051FEEFD4E66</a:ObjectID>
<a:Name>说明信息</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590113594</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>说明信息</a:Comment>
<a:DataType>varchar(512)</a:DataType>
<a:Length>512</a:Length>
</o:Column>
<o:Column Id="o133">
<a:ObjectID>4987377B-561C-4D3E-AADE-B633E8519776</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>ctime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117489</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o134">
<a:ObjectID>51961C62-28FE-4EF5-97AA-5FC2A35D1D43</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>utime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590246481</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o135">
<a:ObjectID>4868846D-0D4A-4768-9B10-B24316799691</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o119"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o136">
<a:ObjectID>F5B506B9-8635-445A-A973-A86D9FFD17A2</a:ObjectID>
<a:Name>index_uid</a:Name>
<a:Code>index_uid</a:Code>
<a:CreationDate>1590113683</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590113798</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o137">
<a:ObjectID>E78F67B1-2794-485D-9F49-114A58F48256</a:ObjectID>
<a:CreationDate>1590113754</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590113781</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Column>
<o:Column Ref="o124"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o138">
<a:ObjectID>15603923-0383-4414-A5DF-ED7578158EFB</a:ObjectID>
<a:Name>index_ctime</a:Name>
<a:Code>index_ctime</a:Code>
<a:CreationDate>1590113922</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117489</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o139">
<a:ObjectID>6A814B58-C125-458A-B581-EA2F201F53A0</a:ObjectID>
<a:CreationDate>1590117480</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117489</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Column>
<o:Column Ref="o133"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o140">
<a:ObjectID>BAE1B619-2E28-4278-9E09-49D3B09AC62C</a:ObjectID>
<a:Name>index_utime</a:Name>
<a:Code>index_utime</a:Code>
<a:CreationDate>1590237577</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590246481</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o141">
<a:ObjectID>7178A11E-9DC7-4648-8D34-14FDB8A2BEDE</a:ObjectID>
<a:CreationDate>1590246474</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590246481</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Column>
<o:Column Ref="o134"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o142">
<a:ObjectID>AC53F5D6-278E-4C37-9DE6-98B2784ECB96</a:ObjectID>
<a:Name>uniq_seq</a:Name>
<a:Code>uniq_seq</a:Code>
<a:CreationDate>1591443612</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591443764</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o143">
<a:ObjectID>DC05C37D-4F08-451D-ADAB-BE3A77D235CB</a:ObjectID>
<a:CreationDate>1591443749</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591443755</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Column>
<o:Column Ref="o120"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o135"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o38">
<a:ObjectID>B55429AD-3AA7-4733-B036-15FD58C3B223</a:ObjectID>
<a:Name>奖包项目</a:Name>
<a:Code>ge_lottery_package_item</a:Code>
<a:CreationDate>1406015220</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186828</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包项目</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o144">
<a:ObjectID>B7D7EAF9-3E8E-495B-95F0-6C31D468B453</a:ObjectID>
<a:Name>奖包标识</a:Name>
<a:Code>package_id</a:Code>
<a:CreationDate>1406015227</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590112284</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o145">
<a:ObjectID>F8F664C6-B1CF-471C-B801-622442B525D6</a:ObjectID>
<a:Name>所属业务</a:Name>
<a:Code>busi_id</a:Code>
<a:CreationDate>1585881120</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590112936</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>所属业务</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o146">
<a:ObjectID>7E23D07F-1964-408A-9000-55A0B2DFC682</a:ObjectID>
<a:Name>项目标识</a:Name>
<a:Code>item_id</a:Code>
<a:CreationDate>1406015227</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182547</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目标识</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o147">
<a:ObjectID>6CE9809C-8191-4651-8B75-BB36B9A2F102</a:ObjectID>
<a:Name>项目类型</a:Name>
<a:Code>item_type</a:Code>
<a:CreationDate>1591175618</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591183187</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目类型</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o148">
<a:ObjectID>4479D8A4-5507-4B21-8A79-C63D816FDBE4</a:ObjectID>
<a:Name>项目名称</a:Name>
<a:Code>item_name</a:Code>
<a:CreationDate>1406016425</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182557</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目名称</a:Comment>
<a:DataType>varchar(256)</a:DataType>
<a:Length>256</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o149">
<a:ObjectID>F5904125-0E32-4E74-8B06-FC324C2D05BD</a:ObjectID>
<a:Name>项目图片</a:Name>
<a:Code>item_image</a:Code>
<a:CreationDate>1406016425</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182563</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目图片</a:Comment>
<a:DataType>varchar(1024)</a:DataType>
<a:Length>1024</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o150">
<a:ObjectID>FDFDE671-7935-42A1-A60F-B80AB5F909F9</a:ObjectID>
<a:Name>项目数量</a:Name>
<a:Code>item_num</a:Code>
<a:CreationDate>1590056363</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182569</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目数量</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o151">
<a:ObjectID>C4B11430-B62A-4794-AA7F-3797B2305546</a:ObjectID>
<a:Name>发放方式</a:Name>
<a:Code>issue_type</a:Code>
<a:CreationDate>1590114649</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591185042</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>发放方式，1-自动，2-人工</a:Comment>
<a:DefaultValue>2</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o152">
<a:ObjectID>A6CF5A26-9E75-4321-AE21-A3680AE84F9E</a:ObjectID>
<a:Name>显示位置</a:Name>
<a:Code>position</a:Code>
<a:CreationDate>1407314835</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117046</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>显示位置，值小排前</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o153">
<a:ObjectID>E29CB985-24B2-4FEA-B6F4-9E30E4AAE5D7</a:ObjectID>
<a:Name>选中时图片</a:Name>
<a:Code>choice_image</a:Code>
<a:CreationDate>1406016515</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117052</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>选中时候的图片</a:Comment>
<a:DataType>varchar(1024)</a:DataType>
<a:Length>1024</a:Length>
</o:Column>
<o:Column Id="o154">
<a:ObjectID>35E9A5FF-0F5A-4244-9C5B-80B7BE410BF7</a:ObjectID>
<a:Name>鼠标移上tips</a:Name>
<a:Code>mouseover_tips</a:Code>
<a:CreationDate>1406016515</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117057</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>鼠标移上去时候的tips</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>65ECDBDD-DCFF-40F8-840B-0B5A1A76DCD8</a:ObjectID>
<a:Name>点跳地址</a:Name>
<a:Code>skip_url</a:Code>
<a:CreationDate>1406016515</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117060</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>点击跳转地址</a:Comment>
<a:DataType>varchar(1024)</a:DataType>
<a:Length>1024</a:Length>
</o:Column>
<o:Column Id="o156">
<a:ObjectID>CB6F16DA-3CE8-4EBA-8F49-BD1224D2BE9F</a:ObjectID>
<a:Name>说明信息</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590112722</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>说明信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o157">
<a:ObjectID>30F57F16-D653-4CFF-A9B5-39947E1E3BA0</a:ObjectID>
<a:Name>扩展信息</a:Name>
<a:Code>extjson</a:Code>
<a:CreationDate>1374314851</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590057071</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>扩展信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o158">
<a:ObjectID>86F04717-E2E1-4D7B-A044-D3947789E9E1</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>ctime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117395</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>46CCF978-00F3-44B1-9DB8-B009A80F4176</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>utime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117395</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o160">
<a:ObjectID>9EA29C8A-6D08-4AA8-80BD-FBDD56FE50C5</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1406015220</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182573</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o144"/>
<o:Column Ref="o145"/>
<o:Column Ref="o146"/>
<o:Column Ref="o147"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o160"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o39">
<a:ObjectID>2E207D05-3949-4A43-BD8D-A85E709A58DA</a:ObjectID>
<a:Name>抽奖福利任务</a:Name>
<a:Code>ge_lottery_task</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591202024</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>抽奖任务</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o161">
<a:ObjectID>344B6A8E-8644-4B6C-AC87-8AD234984364</a:ObjectID>
<a:Name>任务标识</a:Name>
<a:Code>task_id</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590201067</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>任务标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>549A97B5-2571-4F63-A800-9C8FD2822193</a:ObjectID>
<a:Name>任务名称</a:Name>
<a:Code>task_name</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590112898</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>任务名称</a:Comment>
<a:DataType>varchar(256)</a:DataType>
<a:Length>256</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>AE7D4465-8F54-4877-A530-ED7F90383FD3</a:ObjectID>
<a:Name>开放时间</a:Name>
<a:Code>opentime</a:Code>
<a:CreationDate>1372327484</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590112085</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>开放时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o164">
<a:ObjectID>1CD581BB-8B05-4B51-89EC-D61E2E223822</a:ObjectID>
<a:Name>结束时间</a:Name>
<a:Code>endtime</a:Code>
<a:CreationDate>1372584788</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590112085</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>结束时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o165">
<a:ObjectID>0FC56A8B-366B-454D-BF82-296180FEE013</a:ObjectID>
<a:Name>任务状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1372327548</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590116313</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>控制任务的状态，0 无效，1有效</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o166">
<a:ObjectID>02A357E3-3550-4FDF-9AA3-25207BAEEDA8</a:ObjectID>
<a:Name>抽奖模型</a:Name>
<a:Code>model</a:Code>
<a:CreationDate>1590111024</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590249121</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>抽奖模型，1 ~ n，对应模型表序号</a:Comment>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o167">
<a:ObjectID>488DEDA3-A6B4-4F1F-A7AE-9DA0CC0E8B1A</a:ObjectID>
<a:Name>说明信息</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590067918</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>说明信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o168">
<a:ObjectID>5E77E242-F90A-442B-8CEE-C18BE047A5B0</a:ObjectID>
<a:Name>扩展信息</a:Name>
<a:Code>extjson</a:Code>
<a:CreationDate>1374314851</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590055656</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>扩展信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o169">
<a:ObjectID>F3E2BB8E-A9AA-466E-9F3D-30837ABA300C</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>ctime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117334</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o170">
<a:ObjectID>CC633E21-1707-4D4E-864E-D872A3E2827C</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>utime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117338</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o171">
<a:ObjectID>62F84875-7CEF-443D-AF5E-6367030D66BC</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1372327122</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o161"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o171"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o40">
<a:ObjectID>386E7911-DB50-4BFB-B1E0-3798C89D40B4</a:ObjectID>
<a:Name>奖品发放</a:Name>
<a:Code>ge_lottery_issue</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591589050</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖品发放（数据量大了后，可考虑使用task_id做分表）</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o172">
<a:ObjectID>A4228A96-D74E-4A0E-A9AB-B94E680E1E9B</a:ObjectID>
<a:Name>发放标识</a:Name>
<a:Code>issue_id</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591106078</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>发放标识</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o173">
<a:ObjectID>9EFBCC0B-79BA-4F2E-A84D-7B8BA31EAB5C</a:ObjectID>
<a:Name>中奖标识</a:Name>
<a:Code>record_id</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590651996</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>中奖标识</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o174">
<a:ObjectID>C92DC453-4DAC-470E-94D4-068D0F875DF4</a:ObjectID>
<a:Name>奖包标识</a:Name>
<a:Code>package_id</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591597813</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o175">
<a:ObjectID>98C53923-12D0-43E7-8256-B54B4E8B7D54</a:ObjectID>
<a:Name>所属业务</a:Name>
<a:Code>busi_id</a:Code>
<a:CreationDate>1585881120</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591597813</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>所属业务</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o176">
<a:ObjectID>E93989B3-4DAD-47BB-934C-4D4A156AD018</a:ObjectID>
<a:Name>项目标识</a:Name>
<a:Code>item_id</a:Code>
<a:CreationDate>1406015227</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591597813</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目标识</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o177">
<a:ObjectID>963918CF-6AD5-4811-A2F6-6983D64069FC</a:ObjectID>
<a:Name>项目类型</a:Name>
<a:Code>item_type</a:Code>
<a:CreationDate>1591175618</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591597813</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目类型</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o178">
<a:ObjectID>EDF460B4-24CA-4DC3-B5A2-641F6CD5CECC</a:ObjectID>
<a:Name>项目名称</a:Name>
<a:Code>item_name</a:Code>
<a:CreationDate>1406016425</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182855</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目名称</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>81FE4EE0-C4BE-42E0-935A-331F14ABECD8</a:ObjectID>
<a:Name>项目数量</a:Name>
<a:Code>item_num</a:Code>
<a:CreationDate>1590056363</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182861</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目数量</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o180">
<a:ObjectID>676CE0CE-FBE9-4599-B7DC-DA577DD9D1AE</a:ObjectID>
<a:Name>发放状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1406022435</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590246440</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>发放状态: 0-未发, 1-已发,  -1-发失败(自动重发),  -2-情况未明(不自动重发由人工介入)</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o181">
<a:ObjectID>0A8257BE-7DDA-4232-A4DE-E513ADEEFD6F</a:ObjectID>
<a:Name>中奖人UID</a:Name>
<a:Code>uid</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182903</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>中奖人UID</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o182">
<a:ObjectID>01009051-D783-494E-9E6E-AF15967AFEDE</a:ObjectID>
<a:Name>任务标识</a:Name>
<a:Code>task_id</a:Code>
<a:CreationDate>1406022143</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590114396</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>任务标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>F947BF6D-59FE-4000-9852-A2DB976C34EC</a:ObjectID>
<a:Name>额外长整数</a:Name>
<a:Code>extlong</a:Code>
<a:CreationDate>1591588832</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591589104</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>额外长整数，放一些额外信息如辅助统计的值</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>2784DBFE-6D7B-4A44-9867-F7947742AB43</a:ObjectID>
<a:Name>额外字符串</a:Name>
<a:Code>exstr</a:Code>
<a:CreationDate>1591588832</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591589050</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>额外字符串，放一些额外信息如辅助统计的值</a:Comment>
<a:DataType>varchar(64)</a:DataType>
<a:Length>64</a:Length>
</o:Column>
<o:Column Id="o185">
<a:ObjectID>F65A14D7-F923-4E3E-8E74-2F81A60B186B</a:ObjectID>
<a:Name>扩展信息</a:Name>
<a:Code>extjson</a:Code>
<a:CreationDate>1374314851</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590246203</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>扩展信息</a:Comment>
<a:DataType>varchar(512)</a:DataType>
<a:Length>512</a:Length>
</o:Column>
<o:Column Id="o186">
<a:ObjectID>B910DB8C-1757-4477-B4B5-C52463EA7C8D</a:ObjectID>
<a:Name>说明信息</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1590203276</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590227008</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(512)</a:DataType>
<a:Length>512</a:Length>
</o:Column>
<o:Column Id="o187">
<a:ObjectID>6A1BC19F-B706-465F-8F43-636F2CFFFF23</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>ctime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117540</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o188">
<a:ObjectID>91A7DFCF-21C5-4B8B-A115-7290B228E80A</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>utime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591183089</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o189">
<a:ObjectID>89008007-479E-4A92-949A-C86111FDD758</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1406020117</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590651996</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o172"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o190">
<a:ObjectID>E89235EC-3D4E-4EB9-8D83-D05F47C457F9</a:ObjectID>
<a:Name>index_uid</a:Name>
<a:Code>index_uid</a:Code>
<a:CreationDate>1590114462</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117552</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o191">
<a:ObjectID>7728EE33-2D91-4924-B710-A503D7EA17BB</a:ObjectID>
<a:CreationDate>1590117543</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117552</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Column>
<o:Column Ref="o181"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o192">
<a:ObjectID>46C758D1-EAC0-48D8-9D2B-55C7566E9DBE</a:ObjectID>
<a:Name>index_ctime</a:Name>
<a:Code>index_ctime</a:Code>
<a:CreationDate>1590114473</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117540</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o193">
<a:ObjectID>28A4A60F-F263-412A-8D32-F5C3312CC864</a:ObjectID>
<a:CreationDate>1590117531</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590117540</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Column>
<o:Column Ref="o187"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o194">
<a:ObjectID>00364BC6-CC60-4D7B-ADE9-22209A2C4CE0</a:ObjectID>
<a:Name>index_record_id</a:Name>
<a:Code>index_record_id</a:Code>
<a:CreationDate>1590651845</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591597813</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o195">
<a:ObjectID>38B33165-FBB2-4C9B-A19C-F07834AADE19</a:ObjectID>
<a:CreationDate>1590651925</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590651951</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Column>
<o:Column Ref="o173"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o189"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o42">
<a:ObjectID>DD31C3CE-E67E-4DD5-96DF-F35F118D74BC</a:ObjectID>
<a:Name>生态业务信息表</a:Name>
<a:Code>ge_business</a:Code>
<a:CreationDate>1585881075</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186867</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>登记各种业务，两把key用来做相交平滑key更新</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o196">
<a:ObjectID>1FE8748B-30CA-481D-8150-32A756342A44</a:ObjectID>
<a:Name>业务标识</a:Name>
<a:Code>busi_id</a:Code>
<a:CreationDate>1585881120</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590204810</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>业务标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o197">
<a:ObjectID>2DD52D74-56BA-43CE-A92F-E21304242A76</a:ObjectID>
<a:Name>业务名称</a:Name>
<a:Code>name</a:Code>
<a:CreationDate>1586317991</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590201746</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>业务名称</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>AC73CF99-4186-4F3A-A640-E8B5E391A736</a:ObjectID>
<a:Name>业务状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1372327548</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590204911</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>控制任务的状态，0 无效，1有效</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o199">
<a:ObjectID>91926E6B-4E7C-4EBA-9660-FAC0B6330FC5</a:ObjectID>
<a:Name>第一秘钥</a:Name>
<a:Code>secret_key1</a:Code>
<a:CreationDate>1585881120</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590201746</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>加密通信Key1</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o200">
<a:ObjectID>B86BF3A0-B624-4B0C-805C-2BBFED9688A3</a:ObjectID>
<a:Name>第二秘钥</a:Name>
<a:Code>secret_key2</a:Code>
<a:CreationDate>1585881120</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590201746</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>加密通信Key2</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o201">
<a:ObjectID>2AE3E7E3-03F9-4013-9AF1-90351699C088</a:ObjectID>
<a:Name>业务说明</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1586435460</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590201746</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o202">
<a:ObjectID>C744D6FA-4529-4861-9A13-DDCC14188A54</a:ObjectID>
<a:Name>扩展数据</a:Name>
<a:Code>extjson</a:Code>
<a:CreationDate>1586435518</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590201746</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o203">
<a:ObjectID>2C7BCD4F-2805-4AE0-8B62-DA5F3ACF38B8</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>ctime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590201746</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o204">
<a:ObjectID>F432358C-0F15-46DF-9B98-38DA3EADEBEE</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>utime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590201746</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o205">
<a:ObjectID>0B7EF348-303F-4EF2-AE2C-E927E8C74E10</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1585881139</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1590201746</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o196"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o205"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o43">
<a:ObjectID>125884FB-C31E-4C16-92C9-D6A7FD799171</a:ObjectID>
<a:Name>福利模型</a:Name>
<a:Code>ge_lottery_welfare</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591194378</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>福利模型</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o206">
<a:ObjectID>F0BEC510-DEAE-45B0-8BB4-0ACB49710B07</a:ObjectID>
<a:Name>任务标识</a:Name>
<a:Code>task_id</a:Code>
<a:CreationDate>1406016637</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591148152</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>任务标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o207">
<a:ObjectID>28FCE660-4D38-4CA6-90BE-EE8C60699181</a:ObjectID>
<a:Name>奖包标识</a:Name>
<a:Code>package_id</a:Code>
<a:CreationDate>1406015227</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591148152</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o208">
<a:ObjectID>B511E832-671C-4A0F-9BBC-A9B4357CACC4</a:ObjectID>
<a:Name>显示位置</a:Name>
<a:Code>position</a:Code>
<a:CreationDate>1407314835</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591168459</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>显示位置，值小排前</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o209">
<a:ObjectID>AFA2D29E-4189-4B4B-AE41-6FE60B8D0EC9</a:ObjectID>
<a:Name>福利名称</a:Name>
<a:Code>welfare_name</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591148502</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>福利名称</a:Comment>
<a:DataType>varchar(256)</a:DataType>
<a:Length>256</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o210">
<a:ObjectID>6DBAE12F-D544-4F6F-A4E9-285E9EE7CAB9</a:ObjectID>
<a:Name>总体上限</a:Name>
<a:Code>total_limit</a:Code>
<a:CreationDate>1590496363</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591109184</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>总体上限，小于0无限制</a:Comment>
<a:DefaultValue>-1</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o211">
<a:ObjectID>F4A4CBF4-4829-4381-B8C0-6AC18A1BEC4D</a:ObjectID>
<a:Name>用户上限</a:Name>
<a:Code>user_limit</a:Code>
<a:CreationDate>1590500985</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591109184</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>用户上限，小于0无限制</a:Comment>
<a:DefaultValue>-1</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o212">
<a:ObjectID>45864F9E-8557-4F49-9635-C529F2D3B097</a:ObjectID>
<a:Name>每日上限</a:Name>
<a:Code>daily_limit</a:Code>
<a:CreationDate>1590496363</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591109184</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>每日上限，小于0无限制</a:Comment>
<a:DefaultValue>-1</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o213">
<a:ObjectID>01CDCC11-9703-447C-BACF-F752793FB7C4</a:ObjectID>
<a:Name>日限额统计分组</a:Name>
<a:Code>daily_limit_group</a:Code>
<a:CreationDate>1591194012</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591234896</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>日限额统计分组，若空则在 package_id 上统计</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
</o:Column>
<o:Column Id="o214">
<a:ObjectID>EB5CEB25-3DEF-4886-9DAF-655C8E83B8C3</a:ObjectID>
<a:Name>已消耗数量</a:Name>
<a:Code>consumed</a:Code>
<a:CreationDate>1406023168</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591156129</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>已消耗数量</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o215">
<a:ObjectID>841D70FD-9A49-47D3-99CA-8AC194CEF5FC</a:ObjectID>
<a:Name>项目状态</a:Name>
<a:Code>status</a:Code>
<a:CreationDate>1372327548</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591156291</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>控制任务的状态，0 无效，1有效</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o216">
<a:ObjectID>E071A946-ACD4-4255-AAF8-3CBDBF02252A</a:ObjectID>
<a:Name>说明信息</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591109184</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>说明信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o217">
<a:ObjectID>BAA6FE50-8C2F-4C67-A2CC-23DA5BE82968</a:ObjectID>
<a:Name>扩展信息</a:Name>
<a:Code>extjson</a:Code>
<a:CreationDate>1374314851</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591109184</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>扩展信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o218">
<a:ObjectID>053B65FF-87DF-4BF3-8D6C-4E5B68429BDF</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>ctime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591109184</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o219">
<a:ObjectID>F708AD10-CF2B-4479-AE82-5A9B1E3947D6</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>utime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591109184</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o220">
<a:ObjectID>1EB5684F-9B05-4A48-B61B-FB085F4C4C94</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591148152</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o206"/>
<o:Column Ref="o207"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o220"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o44">
<a:ObjectID>C9BDB7E7-BA93-4C97-9FB4-9071C85CE611</a:ObjectID>
<a:Name>业务项目表</a:Name>
<a:Code>ge_business_item</a:Code>
<a:CreationDate>1406015220</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186867</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>业务项目表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o221">
<a:ObjectID>1F4CDB75-CFB9-4415-8541-040D100C3113</a:ObjectID>
<a:Name>所属业务</a:Name>
<a:Code>busi_id</a:Code>
<a:CreationDate>1585881120</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591175569</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>所属业务</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o222">
<a:ObjectID>2790A0A6-1948-4209-9851-73A63AFB96DF</a:ObjectID>
<a:Name>项目标识</a:Name>
<a:Code>item_id</a:Code>
<a:CreationDate>1406015227</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182383</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目标识</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o223">
<a:ObjectID>9FC24880-939C-4092-8C21-9C88AF443107</a:ObjectID>
<a:Name>项目类型</a:Name>
<a:Code>item_type</a:Code>
<a:CreationDate>1591175618</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591183753</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目类型, 0-未知</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o224">
<a:ObjectID>CC4AC2F6-29E2-4568-BEFA-19DF61B50A5C</a:ObjectID>
<a:Name>项目名称</a:Name>
<a:Code>item_name</a:Code>
<a:CreationDate>1406016425</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182415</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目名称</a:Comment>
<a:DataType>varchar(256)</a:DataType>
<a:Length>256</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o225">
<a:ObjectID>E5715088-671C-4FE0-817F-41B09B484E02</a:ObjectID>
<a:Name>项目图片</a:Name>
<a:Code>item_image</a:Code>
<a:CreationDate>1406016425</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182424</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>项目图片</a:Comment>
<a:DataType>varchar(1024)</a:DataType>
<a:Length>1024</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o226">
<a:ObjectID>21FA9693-D180-4A63-ACB5-7E9F0EC46B60</a:ObjectID>
<a:Name>说明信息</a:Name>
<a:Code>remark</a:Code>
<a:CreationDate>1372327122</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591175569</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>说明信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o227">
<a:ObjectID>615FF34C-D734-42A8-A778-A430FF1A3FFE</a:ObjectID>
<a:Name>扩展信息</a:Name>
<a:Code>extjson</a:Code>
<a:CreationDate>1374314851</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591175569</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>扩展信息</a:Comment>
<a:DataType>varchar(4000)</a:DataType>
<a:Length>4000</a:Length>
</o:Column>
<o:Column Id="o228">
<a:ObjectID>CEEA01D3-EA70-4FB4-A499-A2E108771959</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>ctime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591175569</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o229">
<a:ObjectID>AD2AB50E-E712-4DF3-9EFB-781C0CE2AFF4</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>utime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591175569</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o230">
<a:ObjectID>EC074D9E-B789-4107-B344-6CA2064D4DA2</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1406015220</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591182225</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o221"/>
<o:Column Ref="o222"/>
<o:Column Ref="o223"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o230"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o46">
<a:ObjectID>BBF81A69-B6D9-4DC8-BA6E-3CB84DC4B49B</a:ObjectID>
<a:Name>日消耗累计</a:Name>
<a:Code>ge_lottery_daily_cost</a:Code>
<a:CreationDate>1406015220</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591191020</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>日消耗累计</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o231">
<a:ObjectID>B21F46D7-CFDA-4E8E-9216-88BF7B3F717C</a:ObjectID>
<a:Name>任务标识</a:Name>
<a:Code>task_id</a:Code>
<a:CreationDate>1406016637</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591188593</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>任务标识</a:Comment>
<a:DataType>int</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o232">
<a:ObjectID>5781F9A6-0D4A-4AA3-8E92-321389B7EBCD</a:ObjectID>
<a:Name>计算指标</a:Name>
<a:Code>calc_index</a:Code>
<a:CreationDate>1406015227</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591193730</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>奖包标识</a:Comment>
<a:DataType>varchar(128)</a:DataType>
<a:Length>128</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o233">
<a:ObjectID>273CE158-1C8C-44A1-915B-0AE0A5B48692</a:ObjectID>
<a:Name>计算日期</a:Name>
<a:Code>calc_date</a:Code>
<a:CreationDate>1591188170</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591190687</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>char(8)</a:DataType>
<a:Length>8</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o234">
<a:ObjectID>80D27D41-2D89-4A3C-B462-F9F53738D74F</a:ObjectID>
<a:Name>消耗数量</a:Name>
<a:Code>consumed</a:Code>
<a:CreationDate>1406023168</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591188170</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>已消耗数量</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o235">
<a:ObjectID>52B0B5CA-80E5-473E-A18D-21E494E4DA47</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>ctime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591188170</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o236">
<a:ObjectID>16CC33A6-1FDA-44D8-B6C9-A65D10FA45DB</a:ObjectID>
<a:Name>修改时间</a:Name>
<a:Code>utime</a:Code>
<a:CreationDate>1586435534</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591188170</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>datetime</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o237">
<a:ObjectID>E01B17E3-773D-4A4A-93F6-193753F62E9E</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1406015220</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591190687</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o231"/>
<o:Column Ref="o232"/>
<o:Column Ref="o233"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o237"/>
</c:PrimaryKey>
</o:Table>
</c:Tables>
<c:References>
<o:Reference Id="o19">
<a:ObjectID>EB5DE1F1-DFC0-4F2D-8BD8-C7C41B55161E</a:ObjectID>
<a:Name>item_ref_business</a:Name>
<a:Code>item_ref_business</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186867</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,58={208169ED-E14F-4B04-93BD-2FE48112A769},ReferenceMatch,0=

</a:ExtendedAttributesText>
<c:ParentTable>
<o:Table Ref="o42"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o44"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o205"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o238">
<a:ObjectID>663155CC-0BF4-43E2-95C8-551D2B286EA9</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Column Ref="o196"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o221"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o21">
<a:ObjectID>D36A44F6-C081-4BE4-B499-C260A77B5BDF</a:ObjectID>
<a:Name>pkgitem_ref_busiitem</a:Name>
<a:Code>pkgitem_ref_busiitem</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186584</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o44"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o38"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o230"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o239">
<a:ObjectID>2310E57C-71BD-4160-A654-5E61521FEE92</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Column Ref="o221"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o145"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o240">
<a:ObjectID>D2D44F2F-861B-4FA6-B81B-2855B180A5F8</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Column Ref="o222"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o146"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o241">
<a:ObjectID>E3A674FE-78E4-4F0C-BE0D-AADF3D0B6C2E</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Column Ref="o223"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o147"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o23">
<a:ObjectID>65D14F3D-AF4E-425C-8C00-346B2FA3B504</a:ObjectID>
<a:Name>pkgitem_ref_package</a:Name>
<a:Code>pkgitem_ref_package</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186828</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o35"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o38"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o103"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o242">
<a:ObjectID>355E390E-83A1-42FD-BBA6-9ACDDAE6002F</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Column Ref="o92"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o144"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o27">
<a:ObjectID>064385B3-B4F7-4586-8624-ED100376F8F5</a:ObjectID>
<a:Name>model1_ref_task</a:Name>
<a:Code>model1_ref_task</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186760</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o39"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o36"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o171"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o243">
<a:ObjectID>F21FBE0E-9DCA-4B3C-8571-65485E3EA7D5</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Column Ref="o161"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o104"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o29">
<a:ObjectID>AA75AD90-A16C-4F88-BC79-003866289DF0</a:ObjectID>
<a:Name>model1_ref_package</a:Name>
<a:Code>model1_ref_package</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186802</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o35"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o36"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o103"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o244">
<a:ObjectID>084B9B1E-47DC-4251-A7FD-D9D794B89FDC</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Column Ref="o92"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o105"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o32">
<a:ObjectID>3FC3F5AE-A32B-4D9A-B833-A02B958F23A0</a:ObjectID>
<a:Name>welfare_ref_task</a:Name>
<a:Code>welfare_ref_task</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186778</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o39"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o43"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o171"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o245">
<a:ObjectID>7B8658A8-81B1-4A35-B374-6A6405BC54B3</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Column Ref="o161"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o206"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o34">
<a:ObjectID>DE888D46-EB07-4668-BF91-AA694692AEF2</a:ObjectID>
<a:Name>welfare_ref_package</a:Name>
<a:Code>welfare_ref_package</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1591186813</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<a:UpdateConstraint>1</a:UpdateConstraint>
<a:DeleteConstraint>1</a:DeleteConstraint>
<c:ParentTable>
<o:Table Ref="o35"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o43"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o103"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o246">
<a:ObjectID>8FE50E3A-297F-4EFF-86A7-4899EECEFDA3</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Column Ref="o92"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o207"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
</c:References>
<c:DefaultGroups>
<o:Group Id="o247">
<a:ObjectID>EF59A7ED-36B9-472D-9E02-A9542BC273D8</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1372327097</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1372327097</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:ChildTraceabilityLinks>
<o:ExtendedDependency Id="o9">
<a:ObjectID>9240A3C5-21ED-4378-975B-8E178235D3A3</a:ObjectID>
<a:CreationDate>1408517081</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1408517081</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o35"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o37"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o12">
<a:ObjectID>6EC7C7F4-DF5F-4A2C-91F0-071E9434466D</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o37"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o40"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o15">
<a:ObjectID>C0E5059E-0374-465C-90E4-79D4272C6587</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o38"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o40"/>
</c:Object2>
</o:ExtendedDependency>
</c:ChildTraceabilityLinks>
<c:TargetModels>
<o:TargetModel Id="o248">
<a:ObjectID>7AA9EE9A-CCA9-4C30-85F9-64E92726095B</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1372327097</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1534747503</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:TargetModelURL>file:///d|/Program Files (x86)/Sybase/PowerDesigner 16/Resource Files/DBMS/mysql50.xdb</a:TargetModelURL>
<a:TargetModelID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<a:TargetModelLastModificationDate>1276524678</a:TargetModelLastModificationDate>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>