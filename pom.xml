<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                             http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.yy.hdzk</groupId>
	<artifactId>hdpt_activity_xlogic_parent</artifactId>
	<packaging>pom</packaging>
	<version>1.0.0</version>
	<name>HDPT Activity Xlogic - Parent</name>

	<modules>
		<module>hdpt_activity_xlogic_common</module>
		<module>hdpt_activity_xlogic_handler</module>
		<module>hdpt_activity_xlogic_base</module>
		<module>hdpt_activity_xlogic_app</module>
    </modules>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!-- <spring_boot_version>1.5.4.RELEASE</spring_boot_version>  -->
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>

		<spring_boot_version>2.7.18</spring_boot_version>

		<skip_maven_deploy>true</skip_maven_deploy>
		<dubbo_yrpc_version>********-RELEASE</dubbo_yrpc_version>
		<slf4j_api_version>1.7.26</slf4j_api_version>
		<logback_version>1.2.5</logback_version>
		<ali-sentinel.version>1.8.6</ali-sentinel.version>
		<fostress.version>1.2.1-SNAPSHOT</fostress.version>
		<log4j2.version>2.17.2</log4j2.version>
		<yy-boot.version>2.2.16</yy-boot.version>
	</properties>

	<dependencyManagement>

		<dependencies>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-bom</artifactId>
				<version>${log4j2.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<!-- Import dependency management from Spring Boot -->
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring_boot_version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-test</artifactId>
				<version>${spring_boot_version}</version>
				<scope>test</scope>
			</dependency>

			<!-- MYSQL -->
			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>5.1.47</version>
			</dependency>

			<!-- memcached 依赖 -->
			<dependency>
				<groupId>com.google.code.simple-spring-memcached</groupId>
				<artifactId>xmemcached-provider</artifactId>
				<version>3.6.1</version>
				<exclusions>
					<exclusion>
						<groupId>org.aspectj</groupId>
						<artifactId>aspectjweaver</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.google.code.simple-spring-memcached</groupId>
				<artifactId>spring-cache</artifactId>
				<version>3.6.1</version>
			</dependency>

			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>31.1-jre</version>
			</dependency>

			<dependency>
				<groupId>org.codehaus.jackson</groupId>
				<artifactId>jackson-jaxrs</artifactId>
				<version>1.9.13</version>
			</dependency>
			<dependency>
				<groupId>org.codehaus.jackson</groupId>
				<artifactId>jackson-mapper-asl</artifactId>
				<version>1.9.13</version>
			</dependency>
			<dependency>
				<groupId>org.codehaus.jackson</groupId>
				<artifactId>jackson-core-asl</artifactId>
				<version>1.9.13</version>
			</dependency>
			<dependency>
				<groupId>org.codehaus.janino</groupId>
				<artifactId>janino</artifactId>
				<version>3.1.6</version>
			</dependency>
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>1.9.3</version>
			</dependency>

			<dependency>
				<groupId>org.apache.thrift</groupId>
				<artifactId>libthrift</artifactId>
				<version>0.9.3</version>
			</dependency>

			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>1.4</version>
			</dependency>


			<!-- begin: udb login jar info -->
			<dependency>
				<groupId>cn.huanju.udb.lgn</groupId>
				<artifactId>ca-client</artifactId>
				<version>1.8.7-20160420</version>
			</dependency>
			<dependency>
				<groupId>cn.huanju.udb.lgn</groupId>
				<artifactId>udb-client-intranet</artifactId>
				<version>1.0.1</version>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>javax.servlet</groupId>
						<artifactId>servlet-api</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>cn.huanju.udb.lgn</groupId>
				<artifactId>ca-common</artifactId>
				<version>1.8-20160428</version>
			</dependency>
			<dependency>
				<groupId>cn.huanju.udb.lgn</groupId>
				<artifactId>oauth</artifactId>
				<version>20150518</version>
			</dependency>
			<dependency>
				<groupId>cn.huanju.udb.lgn</groupId>
				<artifactId>oauth-consumer</artifactId>
				<version>20150518</version>
			</dependency>
			<dependency>
				<groupId>cn.huanju.udb.lgn</groupId>
				<artifactId>oauth-httpclient3</artifactId>
				<version>20151215</version>
			</dependency>
			<dependency>
				<groupId>commons-httpclient</groupId>
				<artifactId>commons-httpclient</artifactId>
				<version>3.1</version>
			</dependency>
			<dependency>
				<groupId>commons-collections</groupId>
				<artifactId>commons-collections</artifactId>
				<version>3.2.1</version>
			</dependency>
			<dependency>
			  <groupId>com.google.code.gson</groupId>
			  <artifactId>gson</artifactId>
			  <version>2.8.5</version>
			</dependency>
			<!-- end: udb login jar info -->

			<!-- office excel -->
			<dependency>
				<groupId>net.sourceforge.jexcelapi</groupId>
				<artifactId>jxl</artifactId>
				<version>2.6.3</version>
			</dependency>

			<dependency>
				<groupId>com.yy.aomi</groupId>
				<artifactId>aomi-sdk-all</artifactId>
				<version>1.6.0.1</version>
				<exclusions>
					<exclusion>
						<groupId>com.yy.aomi</groupId>
						<artifactId>aomi-sdk-dubbo</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.aspectj</groupId>
						<artifactId>aspectjweaver</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.aspectj</groupId>
						<artifactId>aspectjrt</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>org.reflections</groupId>
				<artifactId>reflections</artifactId>
				<version>0.9.10</version>
			</dependency>

			<dependency>
				<groupId>c3p0</groupId>
				<artifactId>c3p0</artifactId>
				<version>0.9.1.2</version>
			</dependency>

			<!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>1.2.83</version>
			</dependency>

			<dependency>
			  <groupId>junit</groupId>
			  <artifactId>junit</artifactId>
			  <version>4.12</version>
			</dependency>

			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-all</artifactId>
				<version>${dubbo_yrpc_version}</version>
			</dependency>

			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-api</artifactId>
				<version>${slf4j_api_version}</version>
			</dependency>

			<!-- begin: 接入YY S2S -->
			<dependency>
	            <groupId>com.yy.ent.clients</groupId>
	            <artifactId>clients-daemon</artifactId>
				<version>4.3.6.3-RELEASE</version>
	        </dependency>
			<!-- begin: 接入YY S2S -->

			<!-- begin: jserverlib -->
			<dependency>
				<groupId>com.yy.jserverlib</groupId>
				<artifactId>jserverlib-core</artifactId>
				<version>1.7.9-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>io.netty</groupId>
						<artifactId>netty</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.yy.jserverlib</groupId>
				<artifactId>jserverlib-sal</artifactId>
				<version>1.7.11-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.yy.jserverlib</groupId>
				<artifactId>jserverlib-rpc</artifactId>
				<version>1.7.8-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>3.5</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-pool2</artifactId>
				<version>2.11.1</version>
			</dependency>
			<!-- end: jserverlib -->

			<dependency>
				<groupId>com.yy.yxst</groupId>
				<artifactId>yxst-lib-svc-sdk</artifactId>
				<version>2.3.2-SNAPSHOT</version>
			</dependency>

			<!-- https://mvnrepository.com/artifact/org.hibernate.validator/hibernate-validator -->
			<dependency>
				<groupId>org.hibernate.validator</groupId>
				<artifactId>hibernate-validator</artifactId>
				<version>6.1.5.Final</version>
			</dependency>

			<dependency>
				<groupId>com.yy.boot.component</groupId>
				<artifactId>yy-spring-boot-dependencies</artifactId>
				<version>${yy-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>com.yy.boot</groupId>
				<artifactId>y-boot-starter-thread-pool</artifactId>
				<version>1.0.30-RELEASE</version>
			</dependency>

			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjweaver</artifactId>
				<version>1.9.21</version>
			</dependency>

			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjrt</artifactId>
				<version>1.9.21</version>
			</dependency>
		</dependencies>


	</dependencyManagement>

	<repositories>
		<!-- yy ent（entertainment） -->
		<repository>
			<id>yyent</id>
			<name>YYEnt Public Repositories</name>
			<url>https://nexus.yy.com/music/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>

		<repository>
			<id>public</id>
			<name>YYEnt Public Repositories</name>
			<url>https://nexus.yy.com/music/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>

</project>
