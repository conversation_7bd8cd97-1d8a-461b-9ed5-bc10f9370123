package com.yy.gameecology.common.consts;

/**
 * ge_parameter 表中 name 字段值
 */
public class GeParamName {
    public static final String GE_ONLINE_CHANNEL_LOAD_DATA_THREAD = "ge_online_channel_load_data_thread";

    public static final String HDZT_MAX_RANK_COUNT = "hdzt_max_rank_count";
    public static final String HDZT_MAX_EXPORT_RANK_COUNT = "hdzt_max_export_rank_count";

    public static final String IS_CLOSE_GET_USER_SUB = "is_close_get_user_sub";
    public static final String IS_CLOSE_LAYER_BRO = "is_close_layerBro_";

    public static final String ND_YCOIN_TASK = "nd_ycoin_task_";

    public static final String WECHAT_ROBOT_MSG_NOTIFY_URL = "wechat_robot_msg_notify_url";

    /**
     * svcsdk广播开关, 根据uri区分 SVCSDK_BROADCAST_SWITCH:{URI} 默认1-打开, 0-关闭
     */
    public static final String SVCSDK_BROADCAST_SWITCH = "SVCSDK_BROADCAST_SWITCH:";

    /**
     * SvcSDKService运维数据上报开关，1-开启上报，0-关闭上报
     */
    public static final String SVC_SDK_REPORT_SWITCH = "svc_sdk_report_switch";

    /**
     * 在线频道数据从redis缓存中获取开关： 1-从redis获取，0-不从redis获取
     */
    public static final String ONLINE_CHANNEL_LOAD_DATA_FROM_REDIS = "online_channel_load_data_from_redis";

    /**
     * 加载数据全局定时器锁的锁定时长
     */
    public static final String ONLINE_CHANNEL_LOAD_DATA_LOCK_SECONDS = "online_channel_load_data_lock_seconds";


    /**
     * 加载数据全局定时器锁的锁定时长
     */
    public static final String ONLINE_SUB_CHANNEL_LOAD_DATA_LOCK_SECONDS = "online_sub_channel_load_data_lock_seconds";

    /**
     * 通过缓存获取我的抽奖记录：1-使用缓存， 非1-不使用缓存
     */
    public static final String GET_MY_AWARD_RECORDS_BY_CACHE = "get_my_award_records_by_cache";

    /**
     * 通过缓存获取轮播抽奖记录：1-使用缓存， 非1-不使用缓存
     */
    public static final String GET_ROLL_AWARD_RECORDS_BY_CACHE = "get_roll_award_records_by_cache";


    /**
     * batchAwardByFile 接口的白名单uid
     */
    public static final String BATCH_AWARD_BY_FILE_UIDS = "batch_award_by_file_uids";


    /**
     * 交友浮层广播线程数
     */
    public static final String JY_LAYER_BRO_THREAD_AMOUNT = "jy_layer_bro_thread_amount";

    /**
     * 约战浮层广播线程数
     */
    public static final String YZ_LAYER_BRO_THREAD_AMOUNT = "yz_layer_bro_thread_amount";

    /**
     * 宝贝浮层广播线程数
     */
    public static final String BABY_LAYER_BRO_THREAD_AMOUNT = "baby_layer_bro_thread_amount";

    /**
     * 陪玩浮层广播线程数
     */
    public static final String PW_LAYER_BRO_THREAD_AMOUNT = "pw_layer_bro_thread_amount";

    /**
     * 技能卡浮层广播线程数
     */
    public static final String SKILL_CARD_LAYER_BRO_THREAD_AMOUNT = "skillCard_layer_bro_thread_amount";

    /**
     * 自定义广播线程数
     */
    public static final String CUSTOMER_LAYER_BRO_THREAD_AMOUNT = "customer_layer_bro_thread_amount";

    /**
     * 挂件定时广播常规频率（秒）
     */
    public static final String LAYER_BRO_NORMAL_FREQUENCY = "layer_bro_normal_frequency";

    /**
     * 挂件定时广播快速频率（秒）
     */
    public static final String LAYER_BRO_FAST_FREQUENCY = "layer_bro_fast_frequency";

    /**
     * 挂件定时更新采用快速频率的每小时开始时间（秒）
     */
    public static final String LAYER_BRO_FAST_FREQUENCY_START = "layer_bro_fast_frequency_start";

    /**
     * 挂件定时更新采用快速频率的每小时结束时间（秒）
     */
    public static final String LAYER_BRO_FAST_FREQUENCY_END = "layer_bro_fast_frequency_end";

    /**
     * 挂件聚合更新粒度（秒）
     */
    public static final String ACT_LAYER_REFRESH_SECONDS = "act_layer_refresh_seconds";


    /**
     * doTakeOffPlane 接口的白名单uid
     */
    public static final String DO_TASK_OFF_PLANE_UIDS = "do_task_off_plane_uids";

    /**
     * 活动开始后修复数据或补发广播操作白名单
     */
    public static final String DO_FIX_DATA_OPERA_UID = "do_fix_data_opera_uid";

    /**
     * 快照拦截器开关，关闭的时候不记录请求路径以及不用返回快照数据
     */
    public static final String SNAPSHOT_ASPECT_OPEN = "snapshot_aspect_open";

    /**
     * 生成快照数据开关
     */
    public static final String RUN_GEN_SNAPSHOT_OPEN="run_gen_snapshot_open";

    /**
     * 清理redis数据前，检查是否存在活动定制代码开关
     */
    public static final String CHECK_ACT_CUSTOM_CLASS_OPEN = "check_act_custom_class_open";

    /**
     * 判断是否有效活动的开始时间，往前偏移量(毫秒)
     */
    public static final String ACT_ACTIVE_BEGIN_OFFSET = "act_active_begin_offset";

    /**
     * 判断是否有效活动的结束时间，往后偏移量(毫秒)
     */
    public static final String ACT_ACTIVE_END_OFFSET = "act_active_end_offset";

    /**
     * 无法删除的定制代码白名单
     */
    public static final String CHECK_ACT_CUSTOM_CLASS_WHITE_LIST = "check_act_custom_class_white_list";

    /**
     * kafka消费消息时间最大偏移告警
     */
    public static final String KAFKA_MAX_TIME_OFFSET = "kafka_max_time_offset";

    /**
     * 是否真正调用赛宝接口关闭赛事
     */
    public static final String REAL_CLOSE_SAI_BAO_GAME = "real_close_sai_bao_game";

    /**
     * 赛宝查询房间信息官方账号
     */
    public static final String SAI_BAO_QUERY_ROOM_UID = "sai_bao_query_room_uid";

    public static final String SB_ROOM_NAME = "sai_bao_room_name";

    public static final String PUBG_SIGN_UP_LIMIT = "pubg_sign_up_limit";

    /**
     * IM 消息群 配置参数名， 具体配置在 ge_parameter 表中
     * img_it_xxx 的群以技术人员使用为主（比如技术人员关心的 系统运行状态，关键节点，结算结果等）
     * img_ni_xxx 的群以非技术人员使用为主（比如产品、运营人员关心的 一些功能统计 等）
     */
    public static class IMGroup {
        /** HDPT奖池自检通知群 - 奖池概率自检专用 */
        public static final String IMG_IT_AWARD_POOL_CHECK = "img_it_award_pool_check";

        /** HDPT运行状态通知群 - 结算消息、关键节点消息、状态异常/正常告知 等（ 比如PK对战设置消息 */
        public static final String IMG_IT_RUNNING_STATUS = "img_it_running_status";

        /** HDPT活动信息报告群 - 系统运行过程中的一些统计、汇总等信息，信息可能比较多，这时可用外部链接来承载更多的内容 */
        public static final String IMG_IT_ACTIVITY_INFO_REPORT = "img_it_activity_info_report";

        /** HDPT活动信息报告群2 - 比如统计汇总较频繁的活动 */
        public static final String IMG_IT_ACTIVITY_INFO_REPORT_TWO = "img_it_activity_info_report_two";

        public static final String JY_RECHARGE_NOTIFY = "jy_recharge_notify";

        /**
         * 产运相关群
         * 互动活动-营收活动信息报告群
         */
        public static final String IMG_ACT_TURNOVER = "img_act_turnover";

        /**
         * 产运相关群
         * 互动活动-PC用增信息报告群
         */
        public static final String IMG_ACT_PC_USER_GROWTH = "img_act_pc_user_growth";

        /**
         * 产运相关群
         * 互动活动-手游用增信息报告群
         */
        public static final String IMG_ACT_MOBILE_USER_GROWTH = "img_act_mobile_user_growth";

    }
}
