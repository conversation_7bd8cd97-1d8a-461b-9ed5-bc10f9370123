
package com.yy.gameecology.common.exception;

import com.yy.gameecology.common.Code;

/**
 * Title:
 * Description:
 * Create Time: 2014-02-25 上午10:35
 * author: wangyan
 * version: 1.0
 */
@SuppressWarnings("serial")
public class SuperException extends RuntimeException {
    // 未知错误（因为YY框架不支持负数编码，只能如此）
    public static final int E_UNKNOWN = 99999;

    // 标识生成失败
    public static final int E_FAIL_GEN_ID = 99998;

    // 签名不正确
    public static final int E_WRONG_MAC = 99997;

    // 无效时间戳
    public static final int E_WRONG_TIMESTAMP = 99996;

    // 参数不正确
    public static final int E_WRONG_PARAM = 99994;

    // 数据库操作失败
    public static final int E_DB_OPER = -2;

    // 参数不能为空
    public static final int E_PARAM_NOTNULL = -3;

    // 数据错误
    public static final int E_DATA_ERROR = -4;

    // 不在IP白名单中
    public static final int E_IP_ALLOW = 99995;

    // 参数非法
    public static final int E_PARAM_ILLEGAL = 99994;

    // 系统配置出错
    public static final int E_CONF_ILLEGAL = 99993;

    // 请求失败，用来提示交易错误信息
    public static final int E_FAIL = 8888;


    /**
     * 时间已截止
     */
    public static final int TIME_END = 99992;

    /**
     * 时间未开始
     */
    public static final int TIME_NOT_BEGIN = 99991;

    /**
     * 黑名单
     */
    public static final int IN_BLACK_LIST = 7777;

    /**
     * 同一实名用户已参与
     */
    public static final int RELATE_UID_HAS_JOIN = 7778;

    /**
     * 奖池为0
     */
    public static final int AWARD_POOL_NOTENOUGH = 7779;

    /**
     * IP聚集
     */
    public static final int RISK_RESULT_FORBID = 3001;

    public static final int RISK_RESULT_UDB_RECHECK = 5001;

    public static final int RISK_RESULT_RECHECK = 4001;

    protected int code = -1;

    protected Object data;

    public SuperException(Code code) {
        super(code.getReason());
        this.code = code.getCode();
    }

    public SuperException(String message, int code) {
        super(message);
        this.code = code;
    }

    public SuperException(String message, int code, Object data) {
        super(message);
        this.code = code;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public Object getData() {
        return data;
    }

    public static int getErrCode(Throwable e) {
        return (e instanceof SuperException) ? ((SuperException) e).getCode() : E_UNKNOWN;
    }

    public static String getErrMessage(Throwable e) {
        return (e instanceof SuperException) ? e.getMessage() : "E9999#网络错误,稍后重试";
    }
}
