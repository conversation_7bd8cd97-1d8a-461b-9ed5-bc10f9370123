package com.yy.gameecology.activity.report;

import com.yy.gameecology.activity.bean.hdzt.BaseEvent;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.common.utils.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.Date;

/**
 * 运维数据上报上下文参数解析器
 * 用于从继承自BaseEvent的类中提取参数，并设置到ReportContext中
 *
 * <AUTHOR>
 * @date 2025-08-05 11:18
 **/
public class ReportContextResolver {

    private static final Logger log = LoggerFactory.getLogger(ReportContextResolver.class);

    /**
     * 从BaseEvent子类中提取参数并设置榜单上下文
     *
     * @param event BaseEvent的子类实例
     * @return 是否成功提取并设置上下文
     */
    public static boolean resolveAndSetRankingContext(BaseEvent event) {
        if (event == null) {
            log.warn("事件对象为null，无法提取上下文参数");
            return false;
        }

        try {
            // 提取基础参数
            Long actId = extractActId(event);
            Long rankId = extractRankId(event);
            Long phaseId = extractPhaseId(event);
            String timeCode = extractTimeCode(event);

            // 设置榜单上下文
            ReportContext.setRankingContext(actId, rankId, phaseId, timeCode);

            log.debug("成功从{}中提取并设置榜单上下文: actId={}, rankId={}, phaseId={}, timeCode={}",
                    event.getClass().getSimpleName(), actId, rankId, phaseId, timeCode);

            return true;

        } catch (Exception e) {
            log.error("从{}中提取参数时发生异常", event.getClass().getSimpleName(), e);
            return false;
        }
    }

    /**
     * 提取活动ID
     *
     * @param event BaseEvent实例
     * @return 活动ID，如果获取失败则返回0
     */
    public static Long extractActId(BaseEvent event) {
        if (event == null) {
            return 0L;
        }

        try {
            long actId = event.getActId();
            return actId != 0 ? actId : 0L;
        } catch (Exception e) {
            log.warn("提取actId时发生异常", e);
            return 0L;
        }
    }

    /**
     * 提取榜单ID
     *
     * @param event BaseEvent实例
     * @return 榜单ID，如果获取失败则返回0
     */
    public static Long extractRankId(BaseEvent event) {
        if (event == null) {
            return 0L;
        }

        try {
            long rankId = event.getRankId();
            return rankId != 0 ? rankId : 0L;
        } catch (Exception e) {
            log.warn("提取rankId时发生异常", e);
            return 0L;
        }
    }

    /**
     * 提取阶段ID
     * 不是所有的子类都有phaseId，如果子类没有的时候返回0
     *
     * @param event BaseEvent实例
     * @return 阶段ID，如果子类没有phaseId字段或获取失败则返回0
     */
    public static Long extractPhaseId(BaseEvent event) {
        if (event == null) {
            return 0L;
        }

        try {
            // 使用反射获取phaseId字段
            Method getPhaseIdMethod = findMethod(event.getClass(), "getPhaseId");
            if (getPhaseIdMethod != null) {
                Object result = getPhaseIdMethod.invoke(event);
                if (result instanceof Long) {
                    return (Long) result;
                } else if (result instanceof Number) {
                    return ((Number) result).longValue();
                }
            }

            log.debug("类{}没有phaseId字段，返回默认值0", event.getClass().getSimpleName());
            return 0L;

        } catch (Exception e) {
            log.debug("从类{}提取phaseId时发生异常，返回默认值0: {}",
                    event.getClass().getSimpleName(), e.getMessage());
            return 0L;
        }
    }

    /**
     * 提取时间编码
     * timeCode的提取方法是从endTime中，根据timeKey算出
     * 不是所有的子类都有timeCode，如果子类没有的时候返回null
     *
     * @param event BaseEvent实例
     * @return 时间编码，如果子类没有相关字段或计算失败则返回null
     */
    public static String extractTimeCode(BaseEvent event) {
        if (event == null) {
            return null;
        }

        try {
            // 获取endTime字段
            String endTime = extractEndTime(event);
            if (StringUtils.isEmpty(endTime)) {
                log.debug("类{}没有endTime字段或endTime为空，无法计算timeCode", event.getClass().getSimpleName());
                return null;
            }

            // 获取timeKey字段
            Long timeKey = extractTimeKey(event);
            if (timeKey == null || timeKey == 0) {
                log.debug("类{}没有timeKey字段或timeKey为0，无法计算timeCode", event.getClass().getSimpleName());
                return null;
            }

            // 将endTime字符串转换为Date对象
            Date endDate = DateUtil.getDate(endTime, DateUtil.DEFAULT_PATTERN);
            if (endDate == null) {
                log.warn("无法解析endTime: {}", endTime);
                return null;
            }

            // 根据timeKey和endTime计算timeCode
            String timeCode = TimeKeyHelper.getTimeCode(timeKey, endDate);

            log.debug("成功计算timeCode: endTime={}, timeKey={}, timeCode={}", endTime, timeKey, timeCode);
            return timeCode;

        } catch (Exception e) {
            log.debug("从类{}计算timeCode时发生异常: {}", event.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * 提取结束时间字符串
     *
     * @param event BaseEvent实例
     * @return 结束时间字符串，如果没有则返回null
     */
    private static String extractEndTime(BaseEvent event) {
        if (event == null) {
            return null;
        }

        try {
            // 使用反射获取endTime字段
            Method getEndTimeMethod = findMethod(event.getClass(), "getEndTime");
            if (getEndTimeMethod != null) {
                Object result = getEndTimeMethod.invoke(event);
                if (result instanceof String) {
                    return (String) result;
                }
            }

            return null;

        } catch (Exception e) {
            log.debug("从类{}提取endTime时发生异常: {}", event.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * 提取时间分榜键
     *
     * @param event BaseEvent实例
     * @return 时间分榜键，如果没有则返回null
     */
    private static Long extractTimeKey(BaseEvent event) {
        if (event == null) {
            return null;
        }

        try {
            // 使用反射获取timeKey字段
            Method getTimeKeyMethod = findMethod(event.getClass(), "getTimeKey");
            if (getTimeKeyMethod != null) {
                Object result = getTimeKeyMethod.invoke(event);
                if (result instanceof Long) {
                    return (Long) result;
                } else if (result instanceof Number) {
                    return ((Number) result).longValue();
                }
            }

            return null;

        } catch (Exception e) {
            log.debug("从类{}提取timeKey时发生异常: {}", event.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * 查找指定类中的方法（包括父类）
     *
     * @param clazz      要查找的类
     * @param methodName 方法名
     * @return 找到的方法，如果没有找到则返回null
     */
    private static Method findMethod(Class<?> clazz, String methodName) {
        if (clazz == null || StringUtils.isEmpty(methodName)) {
            return null;
        }

        try {
            // 首先在当前类中查找
            Method method = clazz.getDeclaredMethod(methodName);
            method.setAccessible(true);
            return method;
        } catch (NoSuchMethodException e) {
            // 在父类中查找
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null && !Object.class.equals(superClass)) {
                return findMethod(superClass, methodName);
            }
        } catch (Exception e) {
            log.debug("查找方法{}时发生异常: {}", methodName, e.getMessage());
        }

        return null;
    }

    /**
     * 批量处理BaseEvent列表，为每个事件设置榜单上下文
     *
     * @param events BaseEvent列表
     * @return 成功处理的事件数量
     */
    public static int batchResolveAndSetRankingContext(BaseEvent... events) {
        if (events == null || events.length == 0) {
            return 0;
        }

        int successCount = 0;
        for (BaseEvent event : events) {
            if (resolveAndSetRankingContext(event)) {
                successCount++;
            }
        }

        log.debug("批量处理{}个事件，成功处理{}个", events.length, successCount);
        return successCount;
    }

    /**
     * 检查BaseEvent子类是否包含指定字段
     *
     * @param event     BaseEvent实例
     * @param fieldName 字段名（对应的getter方法名，如phaseId对应getPhaseId）
     * @return 是否包含指定字段
     */
    public static boolean hasField(BaseEvent event, String fieldName) {
        if (event == null || StringUtils.isEmpty(fieldName)) {
            return false;
        }

        String getterMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        Method method = findMethod(event.getClass(), getterMethodName);
        return method != null;
    }

    /**
     * 获取BaseEvent子类的字段信息摘要
     *
     * @param event BaseEvent实例
     * @return 字段信息摘要字符串
     */
    public static String getFieldSummary(BaseEvent event) {
        if (event == null) {
            return "null";
        }

        StringBuilder summary = new StringBuilder();
        summary.append(event.getClass().getSimpleName()).append("{");

        // 基础字段
        summary.append("actId=").append(extractActId(event));
        summary.append(", rankId=").append(extractRankId(event));

        // 可选字段
        if (hasField(event, "phaseId")) {
            summary.append(", phaseId=").append(extractPhaseId(event));
        }

        if (hasField(event, "timeKey")) {
            summary.append(", timeKey=").append(extractTimeKey(event));
        }

        if (hasField(event, "endTime")) {
            summary.append(", endTime=").append(extractEndTime(event));
        }

        String timeCode = extractTimeCode(event);
        if (timeCode != null) {
            summary.append(", timeCode=").append(timeCode);
        }

        summary.append("}");
        return summary.toString();
    }

    /**
     * 验证BaseEvent是否包含足够的信息来设置榜单上下文
     *
     * @param event BaseEvent实例
     * @return 验证结果信息
     */
    public static ValidationResult validateEvent(BaseEvent event) {
        if (event == null) {
            return new ValidationResult(false, "事件对象为null");
        }

        StringBuilder issues = new StringBuilder();
        boolean isValid = true;

        // 检查必需字段
        Long actId = extractActId(event);
        if (actId == null || actId == 0) {
            issues.append("actId缺失或为0; ");
            isValid = false;
        }

        Long rankId = extractRankId(event);
        if (rankId == null || rankId == 0) {
            issues.append("rankId缺失或为0; ");
            isValid = false;
        }

        // 检查可选字段的一致性
        if (hasField(event, "timeKey") && hasField(event, "endTime")) {
            Long timeKey = extractTimeKey(event);
            String endTime = extractEndTime(event);

            if (timeKey != null && timeKey != 0 && !StringUtils.isEmpty(endTime)) {
                String timeCode = extractTimeCode(event);
                if (StringUtils.isEmpty(timeCode)) {
                    issues.append("timeKey和endTime存在但无法计算timeCode; ");
                }
            }
        }

        String message = isValid ? "验证通过" : issues.toString();
        return new ValidationResult(isValid, message);
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }

        @Override
        public String toString() {
            return String.format("ValidationResult{valid=%s, message='%s'}", valid, message);
        }
    }
}
