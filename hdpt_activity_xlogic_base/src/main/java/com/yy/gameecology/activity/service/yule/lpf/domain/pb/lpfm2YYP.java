// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lpfm2YYP.proto

package com.yy.gameecology.activity.service.yule.lpf.domain.pb;

public final class lpfm2YYP {
    private lpfm2YYP() {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistryLite registry) {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistry registry) {
        registerAllExtensions(
                (com.google.protobuf.ExtensionRegistryLite) registry);
    }

    public interface GetPublishInfoByUidsReqOrBuilder extends
            // @@protoc_insertion_point(interface_extends:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsReq)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * *
         * 调用方服务名 必填
         * </pre>
         *
         * <code>optional string srvname = 1;</code>
         */
        String getSrvname();

        /**
         * <pre>
         * *
         * 调用方服务名 必填
         * </pre>
         *
         * <code>optional string srvname = 1;</code>
         */
        com.google.protobuf.ByteString
        getSrvnameBytes();

        /**
         * <pre>
         * *
         * yy 15013
         * </pre>
         *
         * <code>optional int64 appID = 2;</code>
         */
        long getAppID();

        /**
         * <pre>
         * *
         * 端：1：yy，2：好看，3：百度，4：全民，5：贴吧
         * </pre>
         *
         * <code>optional int32 hostID = 3;</code>
         */
        int getHostID();

        /**
         * <pre>
         * *
         * 过滤规则：
         * 1：过滤加密频道
         * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
         * </pre>
         *
         * <code>repeated uint32 filterRules = 4;</code>
         */
        java.util.List<Integer> getFilterRulesList();

        /**
         * <pre>
         * *
         * 过滤规则：
         * 1：过滤加密频道
         * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
         * </pre>
         *
         * <code>repeated uint32 filterRules = 4;</code>
         */
        int getFilterRulesCount();

        /**
         * <pre>
         * *
         * 过滤规则：
         * 1：过滤加密频道
         * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
         * </pre>
         *
         * <code>repeated uint32 filterRules = 4;</code>
         */
        int getFilterRules(int index);

        /**
         * <pre>
         * *
         * uids
         * </pre>
         *
         * <code>repeated uint64 uids = 5;</code>
         */
        java.util.List<Long> getUidsList();

        /**
         * <pre>
         * *
         * uids
         * </pre>
         *
         * <code>repeated uint64 uids = 5;</code>
         */
        int getUidsCount();

        /**
         * <pre>
         * *
         * uids
         * </pre>
         *
         * <code>repeated uint64 uids = 5;</code>
         */
        long getUids(int index);

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 6;</code>
         */
        int getExtendInfoCount();

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 6;</code>
         */
        boolean containsExtendInfo(
                String key);

        /**
         * Use {@link #getExtendInfoMap()} instead.
         */
        @Deprecated
        java.util.Map<String, String>
        getExtendInfo();

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 6;</code>
         */
        java.util.Map<String, String>
        getExtendInfoMap();

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 6;</code>
         */

        String getExtendInfoOrDefault(
                String key,
                String defaultValue);

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 6;</code>
         */

        String getExtendInfoOrThrow(
                String key);
    }

    /**
     * Protobuf type {@code com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsReq}
     */
    public static final class GetPublishInfoByUidsReq extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsReq)
            GetPublishInfoByUidsReqOrBuilder {
        // Use GetPublishInfoByUidsReq.newBuilder() to construct.
        private GetPublishInfoByUidsReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private GetPublishInfoByUidsReq() {
            srvname_ = "";
            appID_ = 0L;
            hostID_ = 0;
            filterRules_ = java.util.Collections.emptyList();
            uids_ = java.util.Collections.emptyList();
        }

        @Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
            return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
        }

        private GetPublishInfoByUidsReq(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            this();
            int mutable_bitField0_ = 0;
            try {
                boolean done = false;
                while (!done) {
                    int tag = input.readTag();
                    switch (tag) {
                        case 0:
                            done = true;
                            break;
                        default: {
                            if (!input.skipField(tag)) {
                                done = true;
                            }
                            break;
                        }
                        case 10: {
                            String s = input.readStringRequireUtf8();

                            srvname_ = s;
                            break;
                        }
                        case 16: {

                            appID_ = input.readInt64();
                            break;
                        }
                        case 24: {

                            hostID_ = input.readInt32();
                            break;
                        }
                        case 32: {
                            if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                                filterRules_ = new java.util.ArrayList<Integer>();
                                mutable_bitField0_ |= 0x00000008;
                            }
                            filterRules_.add(input.readUInt32());
                            break;
                        }
                        case 34: {
                            int length = input.readRawVarint32();
                            int limit = input.pushLimit(length);
                            if (!((mutable_bitField0_ & 0x00000008) == 0x00000008) && input.getBytesUntilLimit() > 0) {
                                filterRules_ = new java.util.ArrayList<Integer>();
                                mutable_bitField0_ |= 0x00000008;
                            }
                            while (input.getBytesUntilLimit() > 0) {
                                filterRules_.add(input.readUInt32());
                            }
                            input.popLimit(limit);
                            break;
                        }
                        case 40: {
                            if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                                uids_ = new java.util.ArrayList<Long>();
                                mutable_bitField0_ |= 0x00000010;
                            }
                            uids_.add(input.readUInt64());
                            break;
                        }
                        case 42: {
                            int length = input.readRawVarint32();
                            int limit = input.pushLimit(length);
                            if (!((mutable_bitField0_ & 0x00000010) == 0x00000010) && input.getBytesUntilLimit() > 0) {
                                uids_ = new java.util.ArrayList<Long>();
                                mutable_bitField0_ |= 0x00000010;
                            }
                            while (input.getBytesUntilLimit() > 0) {
                                uids_.add(input.readUInt64());
                            }
                            input.popLimit(limit);
                            break;
                        }
                        case 50: {
                            if (!((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
                                extendInfo_ = com.google.protobuf.MapField.newMapField(
                                        ExtendInfoDefaultEntryHolder.defaultEntry);
                                mutable_bitField0_ |= 0x00000020;
                            }
                            com.google.protobuf.MapEntry<String, String>
                                    extendInfo__ = input.readMessage(
                                    ExtendInfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
                            extendInfo_.getMutableMap().put(
                                    extendInfo__.getKey(), extendInfo__.getValue());
                            break;
                        }
                    }
                }
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.setUnfinishedMessage(this);
            } catch (java.io.IOException e) {
                throw new com.google.protobuf.InvalidProtocolBufferException(
                        e).setUnfinishedMessage(this);
            } finally {
                if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                    filterRules_ = java.util.Collections.unmodifiableList(filterRules_);
                }
                if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                    uids_ = java.util.Collections.unmodifiableList(uids_);
                }
                makeExtensionsImmutable();
            }
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_descriptor;
        }

        @SuppressWarnings({"rawtypes"})
        protected com.google.protobuf.MapField internalGetMapField(
                int number) {
            switch (number) {
                case 6:
                    return internalGetExtendInfo();
                default:
                    throw new RuntimeException(
                            "Invalid map field number: " + number);
            }
        }

        protected FieldAccessorTable
        internalGetFieldAccessorTable() {
            return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            GetPublishInfoByUidsReq.class, Builder.class);
        }

        private int bitField0_;
        public static final int SRVNAME_FIELD_NUMBER = 1;
        private volatile Object srvname_;

        /**
         * <pre>
         * *
         * 调用方服务名 必填
         * </pre>
         *
         * <code>optional string srvname = 1;</code>
         */
        public String getSrvname() {
            Object ref = srvname_;
            if (ref instanceof String) {
                return (String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                String s = bs.toStringUtf8();
                srvname_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * *
         * 调用方服务名 必填
         * </pre>
         *
         * <code>optional string srvname = 1;</code>
         */
        public com.google.protobuf.ByteString
        getSrvnameBytes() {
            Object ref = srvname_;
            if (ref instanceof String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (String) ref);
                srvname_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int APPID_FIELD_NUMBER = 2;
        private long appID_;

        /**
         * <pre>
         * *
         * yy 15013
         * </pre>
         *
         * <code>optional int64 appID = 2;</code>
         */
        public long getAppID() {
            return appID_;
        }

        public static final int HOSTID_FIELD_NUMBER = 3;
        private int hostID_;

        /**
         * <pre>
         * *
         * 端：1：yy，2：好看，3：百度，4：全民，5：贴吧
         * </pre>
         *
         * <code>optional int32 hostID = 3;</code>
         */
        public int getHostID() {
            return hostID_;
        }

        public static final int FILTERRULES_FIELD_NUMBER = 4;
        private java.util.List<Integer> filterRules_;

        /**
         * <pre>
         * *
         * 过滤规则：
         * 1：过滤加密频道
         * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
         * </pre>
         *
         * <code>repeated uint32 filterRules = 4;</code>
         */
        public java.util.List<Integer>
        getFilterRulesList() {
            return filterRules_;
        }

        /**
         * <pre>
         * *
         * 过滤规则：
         * 1：过滤加密频道
         * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
         * </pre>
         *
         * <code>repeated uint32 filterRules = 4;</code>
         */
        public int getFilterRulesCount() {
            return filterRules_.size();
        }

        /**
         * <pre>
         * *
         * 过滤规则：
         * 1：过滤加密频道
         * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
         * </pre>
         *
         * <code>repeated uint32 filterRules = 4;</code>
         */
        public int getFilterRules(int index) {
            return filterRules_.get(index);
        }

        private int filterRulesMemoizedSerializedSize = -1;

        public static final int UIDS_FIELD_NUMBER = 5;
        private java.util.List<Long> uids_;

        /**
         * <pre>
         * *
         * uids
         * </pre>
         *
         * <code>repeated uint64 uids = 5;</code>
         */
        public java.util.List<Long>
        getUidsList() {
            return uids_;
        }

        /**
         * <pre>
         * *
         * uids
         * </pre>
         *
         * <code>repeated uint64 uids = 5;</code>
         */
        public int getUidsCount() {
            return uids_.size();
        }

        /**
         * <pre>
         * *
         * uids
         * </pre>
         *
         * <code>repeated uint64 uids = 5;</code>
         */
        public long getUids(int index) {
            return uids_.get(index);
        }

        private int uidsMemoizedSerializedSize = -1;

        public static final int EXTENDINFO_FIELD_NUMBER = 6;

        private static final class ExtendInfoDefaultEntryHolder {
            static final com.google.protobuf.MapEntry<
                    String, String> defaultEntry =
                    com.google.protobuf.MapEntry
                            .<String, String>newDefaultInstance(
                                    lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_ExtendInfoEntry_descriptor,
                                    com.google.protobuf.WireFormat.FieldType.STRING,
                                    "",
                                    com.google.protobuf.WireFormat.FieldType.STRING,
                                    "");
        }

        private com.google.protobuf.MapField<
                String, String> extendInfo_;

        private com.google.protobuf.MapField<String, String>
        internalGetExtendInfo() {
            if (extendInfo_ == null) {
                return com.google.protobuf.MapField.emptyMapField(
                        ExtendInfoDefaultEntryHolder.defaultEntry);
            }
            return extendInfo_;
        }

        public int getExtendInfoCount() {
            return internalGetExtendInfo().getMap().size();
        }

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 6;</code>
         */

        public boolean containsExtendInfo(
                String key) {
            if (key == null) {
                throw new NullPointerException();
            }
            return internalGetExtendInfo().getMap().containsKey(key);
        }

        /**
         * Use {@link #getExtendInfoMap()} instead.
         */
        @Deprecated
        public java.util.Map<String, String> getExtendInfo() {
            return getExtendInfoMap();
        }

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 6;</code>
         */

        public java.util.Map<String, String> getExtendInfoMap() {
            return internalGetExtendInfo().getMap();
        }

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 6;</code>
         */

        public String getExtendInfoOrDefault(
                String key,
                String defaultValue) {
            if (key == null) {
                throw new NullPointerException();
            }
            java.util.Map<String, String> map =
                    internalGetExtendInfo().getMap();
            return map.containsKey(key) ? map.get(key) : defaultValue;
        }

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 6;</code>
         */

        public String getExtendInfoOrThrow(
                String key) {
            if (key == null) {
                throw new NullPointerException();
            }
            java.util.Map<String, String> map =
                    internalGetExtendInfo().getMap();
            if (!map.containsKey(key)) {
                throw new IllegalArgumentException();
            }
            return map.get(key);
        }

        private byte memoizedIsInitialized = -1;

        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            getSerializedSize();
            if (!getSrvnameBytes().isEmpty()) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 1, srvname_);
            }
            if (appID_ != 0L) {
                output.writeInt64(2, appID_);
            }
            if (hostID_ != 0) {
                output.writeInt32(3, hostID_);
            }
            if (getFilterRulesList().size() > 0) {
                output.writeUInt32NoTag(34);
                output.writeUInt32NoTag(filterRulesMemoizedSerializedSize);
            }
            for (int i = 0; i < filterRules_.size(); i++) {
                output.writeUInt32NoTag(filterRules_.get(i));
            }
            if (getUidsList().size() > 0) {
                output.writeUInt32NoTag(42);
                output.writeUInt32NoTag(uidsMemoizedSerializedSize);
            }
            for (int i = 0; i < uids_.size(); i++) {
                output.writeUInt64NoTag(uids_.get(i));
            }
            com.google.protobuf.GeneratedMessageV3
                    .serializeStringMapTo(
                            output,
                            internalGetExtendInfo(),
                            ExtendInfoDefaultEntryHolder.defaultEntry,
                            6);
        }

        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (!getSrvnameBytes().isEmpty()) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, srvname_);
            }
            if (appID_ != 0L) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt64Size(2, appID_);
            }
            if (hostID_ != 0) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt32Size(3, hostID_);
            }
            {
                int dataSize = 0;
                for (int i = 0; i < filterRules_.size(); i++) {
                    dataSize += com.google.protobuf.CodedOutputStream
                            .computeUInt32SizeNoTag(filterRules_.get(i));
                }
                size += dataSize;
                if (!getFilterRulesList().isEmpty()) {
                    size += 1;
                    size += com.google.protobuf.CodedOutputStream
                            .computeInt32SizeNoTag(dataSize);
                }
                filterRulesMemoizedSerializedSize = dataSize;
            }
            {
                int dataSize = 0;
                for (int i = 0; i < uids_.size(); i++) {
                    dataSize += com.google.protobuf.CodedOutputStream
                            .computeUInt64SizeNoTag(uids_.get(i));
                }
                size += dataSize;
                if (!getUidsList().isEmpty()) {
                    size += 1;
                    size += com.google.protobuf.CodedOutputStream
                            .computeInt32SizeNoTag(dataSize);
                }
                uidsMemoizedSerializedSize = dataSize;
            }
            for (java.util.Map.Entry<String, String> entry
                    : internalGetExtendInfo().getMap().entrySet()) {
                com.google.protobuf.MapEntry<String, String>
                        extendInfo__ = ExtendInfoDefaultEntryHolder.defaultEntry.newBuilderForType()
                        .setKey(entry.getKey())
                        .setValue(entry.getValue())
                        .build();
                size += com.google.protobuf.CodedOutputStream
                        .computeMessageSize(6, extendInfo__);
            }
            memoizedSize = size;
            return size;
        }

        private static final long serialVersionUID = 0L;

        @Override
        public boolean equals(final Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof GetPublishInfoByUidsReq)) {
                return super.equals(obj);
            }
            GetPublishInfoByUidsReq other = (GetPublishInfoByUidsReq) obj;

            boolean result = true;
            result = result && getSrvname()
                    .equals(other.getSrvname());
            result = result && (getAppID()
                    == other.getAppID());
            result = result && (getHostID()
                    == other.getHostID());
            result = result && getFilterRulesList()
                    .equals(other.getFilterRulesList());
            result = result && getUidsList()
                    .equals(other.getUidsList());
            result = result && internalGetExtendInfo().equals(
                    other.internalGetExtendInfo());
            return result;
        }

        @Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptorForType().hashCode();
            hash = (37 * hash) + SRVNAME_FIELD_NUMBER;
            hash = (53 * hash) + getSrvname().hashCode();
            hash = (37 * hash) + APPID_FIELD_NUMBER;
            hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
                    getAppID());
            hash = (37 * hash) + HOSTID_FIELD_NUMBER;
            hash = (53 * hash) + getHostID();
            if (getFilterRulesCount() > 0) {
                hash = (37 * hash) + FILTERRULES_FIELD_NUMBER;
                hash = (53 * hash) + getFilterRulesList().hashCode();
            }
            if (getUidsCount() > 0) {
                hash = (37 * hash) + UIDS_FIELD_NUMBER;
                hash = (53 * hash) + getUidsList().hashCode();
            }
            if (!internalGetExtendInfo().getMap().isEmpty()) {
                hash = (37 * hash) + EXTENDINFO_FIELD_NUMBER;
                hash = (53 * hash) + internalGetExtendInfo().hashCode();
            }
            hash = (29 * hash) + unknownFields.hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static GetPublishInfoByUidsReq parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static GetPublishInfoByUidsReq parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static GetPublishInfoByUidsReq parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static GetPublishInfoByUidsReq parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static GetPublishInfoByUidsReq parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static GetPublishInfoByUidsReq parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static GetPublishInfoByUidsReq parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static GetPublishInfoByUidsReq parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static GetPublishInfoByUidsReq parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static GetPublishInfoByUidsReq parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(GetPublishInfoByUidsReq prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @Override
        protected Builder newBuilderForType(
                BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsReq}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsReq)
                GetPublishInfoByUidsReqOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_descriptor;
            }

            @SuppressWarnings({"rawtypes"})
            protected com.google.protobuf.MapField internalGetMapField(
                    int number) {
                switch (number) {
                    case 6:
                        return internalGetExtendInfo();
                    default:
                        throw new RuntimeException(
                                "Invalid map field number: " + number);
                }
            }

            @SuppressWarnings({"rawtypes"})
            protected com.google.protobuf.MapField internalGetMutableMapField(
                    int number) {
                switch (number) {
                    case 6:
                        return internalGetMutableExtendInfo();
                    default:
                        throw new RuntimeException(
                                "Invalid map field number: " + number);
                }
            }

            protected FieldAccessorTable
            internalGetFieldAccessorTable() {
                return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                GetPublishInfoByUidsReq.class, Builder.class);
            }

            // Construct using com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.lpfm2YYP.GetPublishInfoByUidsReq.newBuilder()
            private Builder() {
                maybeForceBuilderInitialization();
            }

            private Builder(
                    BuilderParent parent) {
                super(parent);
                maybeForceBuilderInitialization();
            }

            private void maybeForceBuilderInitialization() {
                if (com.google.protobuf.GeneratedMessageV3
                        .alwaysUseFieldBuilders) {
                }
            }

            public Builder clear() {
                super.clear();
                srvname_ = "";

                appID_ = 0L;

                hostID_ = 0;

                filterRules_ = java.util.Collections.emptyList();
                bitField0_ = (bitField0_ & ~0x00000008);
                uids_ = java.util.Collections.emptyList();
                bitField0_ = (bitField0_ & ~0x00000010);
                internalGetMutableExtendInfo().clear();
                return this;
            }

            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_descriptor;
            }

            public GetPublishInfoByUidsReq getDefaultInstanceForType() {
                return GetPublishInfoByUidsReq.getDefaultInstance();
            }

            public GetPublishInfoByUidsReq build() {
                GetPublishInfoByUidsReq result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            public GetPublishInfoByUidsReq buildPartial() {
                GetPublishInfoByUidsReq result = new GetPublishInfoByUidsReq(this);
                int from_bitField0_ = bitField0_;
                int to_bitField0_ = 0;
                result.srvname_ = srvname_;
                result.appID_ = appID_;
                result.hostID_ = hostID_;
                if (((bitField0_ & 0x00000008) == 0x00000008)) {
                    filterRules_ = java.util.Collections.unmodifiableList(filterRules_);
                    bitField0_ = (bitField0_ & ~0x00000008);
                }
                result.filterRules_ = filterRules_;
                if (((bitField0_ & 0x00000010) == 0x00000010)) {
                    uids_ = java.util.Collections.unmodifiableList(uids_);
                    bitField0_ = (bitField0_ & ~0x00000010);
                }
                result.uids_ = uids_;
                result.extendInfo_ = internalGetExtendInfo();
                result.extendInfo_.makeImmutable();
                result.bitField0_ = to_bitField0_;
                onBuilt();
                return result;
            }

            public Builder clone() {
                return (Builder) super.clone();
            }

            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    Object value) {
                return (Builder) super.setField(field, value);
            }

            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return (Builder) super.clearField(field);
            }

            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return (Builder) super.clearOneof(oneof);
            }

            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, Object value) {
                return (Builder) super.setRepeatedField(field, index, value);
            }

            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    Object value) {
                return (Builder) super.addRepeatedField(field, value);
            }

            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof GetPublishInfoByUidsReq) {
                    return mergeFrom((GetPublishInfoByUidsReq) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(GetPublishInfoByUidsReq other) {
                if (other == GetPublishInfoByUidsReq.getDefaultInstance())
                    return this;
                if (!other.getSrvname().isEmpty()) {
                    srvname_ = other.srvname_;
                    onChanged();
                }
                if (other.getAppID() != 0L) {
                    setAppID(other.getAppID());
                }
                if (other.getHostID() != 0) {
                    setHostID(other.getHostID());
                }
                if (!other.filterRules_.isEmpty()) {
                    if (filterRules_.isEmpty()) {
                        filterRules_ = other.filterRules_;
                        bitField0_ = (bitField0_ & ~0x00000008);
                    } else {
                        ensureFilterRulesIsMutable();
                        filterRules_.addAll(other.filterRules_);
                    }
                    onChanged();
                }
                if (!other.uids_.isEmpty()) {
                    if (uids_.isEmpty()) {
                        uids_ = other.uids_;
                        bitField0_ = (bitField0_ & ~0x00000010);
                    } else {
                        ensureUidsIsMutable();
                        uids_.addAll(other.uids_);
                    }
                    onChanged();
                }
                internalGetMutableExtendInfo().mergeFrom(
                        other.internalGetExtendInfo());
                onChanged();
                return this;
            }

            public final boolean isInitialized() {
                return true;
            }

            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                GetPublishInfoByUidsReq parsedMessage = null;
                try {
                    parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    parsedMessage = (GetPublishInfoByUidsReq) e.getUnfinishedMessage();
                    throw e.unwrapIOException();
                } finally {
                    if (parsedMessage != null) {
                        mergeFrom(parsedMessage);
                    }
                }
                return this;
            }

            private int bitField0_;

            private Object srvname_ = "";

            /**
             * <pre>
             * *
             * 调用方服务名 必填
             * </pre>
             *
             * <code>optional string srvname = 1;</code>
             */
            public String getSrvname() {
                Object ref = srvname_;
                if (!(ref instanceof String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    String s = bs.toStringUtf8();
                    srvname_ = s;
                    return s;
                } else {
                    return (String) ref;
                }
            }

            /**
             * <pre>
             * *
             * 调用方服务名 必填
             * </pre>
             *
             * <code>optional string srvname = 1;</code>
             */
            public com.google.protobuf.ByteString
            getSrvnameBytes() {
                Object ref = srvname_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (String) ref);
                    srvname_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * *
             * 调用方服务名 必填
             * </pre>
             *
             * <code>optional string srvname = 1;</code>
             */
            public Builder setSrvname(
                    String value) {
                if (value == null) {
                    throw new NullPointerException();
                }

                srvname_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * 调用方服务名 必填
             * </pre>
             *
             * <code>optional string srvname = 1;</code>
             */
            public Builder clearSrvname() {

                srvname_ = getDefaultInstance().getSrvname();
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * 调用方服务名 必填
             * </pre>
             *
             * <code>optional string srvname = 1;</code>
             */
            public Builder setSrvnameBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);

                srvname_ = value;
                onChanged();
                return this;
            }

            private long appID_;

            /**
             * <pre>
             * *
             * yy 15013
             * </pre>
             *
             * <code>optional int64 appID = 2;</code>
             */
            public long getAppID() {
                return appID_;
            }

            /**
             * <pre>
             * *
             * yy 15013
             * </pre>
             *
             * <code>optional int64 appID = 2;</code>
             */
            public Builder setAppID(long value) {

                appID_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * yy 15013
             * </pre>
             *
             * <code>optional int64 appID = 2;</code>
             */
            public Builder clearAppID() {

                appID_ = 0L;
                onChanged();
                return this;
            }

            private int hostID_;

            /**
             * <pre>
             * *
             * 端：1：yy，2：好看，3：百度，4：全民，5：贴吧
             * </pre>
             *
             * <code>optional int32 hostID = 3;</code>
             */
            public int getHostID() {
                return hostID_;
            }

            /**
             * <pre>
             * *
             * 端：1：yy，2：好看，3：百度，4：全民，5：贴吧
             * </pre>
             *
             * <code>optional int32 hostID = 3;</code>
             */
            public Builder setHostID(int value) {

                hostID_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * 端：1：yy，2：好看，3：百度，4：全民，5：贴吧
             * </pre>
             *
             * <code>optional int32 hostID = 3;</code>
             */
            public Builder clearHostID() {

                hostID_ = 0;
                onChanged();
                return this;
            }

            private java.util.List<Integer> filterRules_ = java.util.Collections.emptyList();

            private void ensureFilterRulesIsMutable() {
                if (!((bitField0_ & 0x00000008) == 0x00000008)) {
                    filterRules_ = new java.util.ArrayList<Integer>(filterRules_);
                    bitField0_ |= 0x00000008;
                }
            }

            /**
             * <pre>
             * *
             * 过滤规则：
             * 1：过滤加密频道
             * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
             * </pre>
             *
             * <code>repeated uint32 filterRules = 4;</code>
             */
            public java.util.List<Integer>
            getFilterRulesList() {
                return java.util.Collections.unmodifiableList(filterRules_);
            }

            /**
             * <pre>
             * *
             * 过滤规则：
             * 1：过滤加密频道
             * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
             * </pre>
             *
             * <code>repeated uint32 filterRules = 4;</code>
             */
            public int getFilterRulesCount() {
                return filterRules_.size();
            }

            /**
             * <pre>
             * *
             * 过滤规则：
             * 1：过滤加密频道
             * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
             * </pre>
             *
             * <code>repeated uint32 filterRules = 4;</code>
             */
            public int getFilterRules(int index) {
                return filterRules_.get(index);
            }

            /**
             * <pre>
             * *
             * 过滤规则：
             * 1：过滤加密频道
             * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
             * </pre>
             *
             * <code>repeated uint32 filterRules = 4;</code>
             */
            public Builder setFilterRules(
                    int index, int value) {
                ensureFilterRulesIsMutable();
                filterRules_.set(index, value);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * 过滤规则：
             * 1：过滤加密频道
             * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
             * </pre>
             *
             * <code>repeated uint32 filterRules = 4;</code>
             */
            public Builder addFilterRules(int value) {
                ensureFilterRulesIsMutable();
                filterRules_.add(value);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * 过滤规则：
             * 1：过滤加密频道
             * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
             * </pre>
             *
             * <code>repeated uint32 filterRules = 4;</code>
             */
            public Builder addAllFilterRules(
                    Iterable<? extends Integer> values) {
                ensureFilterRulesIsMutable();
                com.google.protobuf.AbstractMessageLite.Builder.addAll(
                        values, filterRules_);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * 过滤规则：
             * 1：过滤加密频道
             * 2：过滤子频道开播仅会员、嘉宾（黄马等）可见的频道
             * </pre>
             *
             * <code>repeated uint32 filterRules = 4;</code>
             */
            public Builder clearFilterRules() {
                filterRules_ = java.util.Collections.emptyList();
                bitField0_ = (bitField0_ & ~0x00000008);
                onChanged();
                return this;
            }

            private java.util.List<Long> uids_ = java.util.Collections.emptyList();

            private void ensureUidsIsMutable() {
                if (!((bitField0_ & 0x00000010) == 0x00000010)) {
                    uids_ = new java.util.ArrayList<Long>(uids_);
                    bitField0_ |= 0x00000010;
                }
            }

            /**
             * <pre>
             * *
             * uids
             * </pre>
             *
             * <code>repeated uint64 uids = 5;</code>
             */
            public java.util.List<Long>
            getUidsList() {
                return java.util.Collections.unmodifiableList(uids_);
            }

            /**
             * <pre>
             * *
             * uids
             * </pre>
             *
             * <code>repeated uint64 uids = 5;</code>
             */
            public int getUidsCount() {
                return uids_.size();
            }

            /**
             * <pre>
             * *
             * uids
             * </pre>
             *
             * <code>repeated uint64 uids = 5;</code>
             */
            public long getUids(int index) {
                return uids_.get(index);
            }

            /**
             * <pre>
             * *
             * uids
             * </pre>
             *
             * <code>repeated uint64 uids = 5;</code>
             */
            public Builder setUids(
                    int index, long value) {
                ensureUidsIsMutable();
                uids_.set(index, value);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * uids
             * </pre>
             *
             * <code>repeated uint64 uids = 5;</code>
             */
            public Builder addUids(long value) {
                ensureUidsIsMutable();
                uids_.add(value);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * uids
             * </pre>
             *
             * <code>repeated uint64 uids = 5;</code>
             */
            public Builder addAllUids(
                    Iterable<? extends Long> values) {
                ensureUidsIsMutable();
                com.google.protobuf.AbstractMessageLite.Builder.addAll(
                        values, uids_);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * uids
             * </pre>
             *
             * <code>repeated uint64 uids = 5;</code>
             */
            public Builder clearUids() {
                uids_ = java.util.Collections.emptyList();
                bitField0_ = (bitField0_ & ~0x00000010);
                onChanged();
                return this;
            }

            private com.google.protobuf.MapField<
                    String, String> extendInfo_;

            private com.google.protobuf.MapField<String, String>
            internalGetExtendInfo() {
                if (extendInfo_ == null) {
                    return com.google.protobuf.MapField.emptyMapField(
                            ExtendInfoDefaultEntryHolder.defaultEntry);
                }
                return extendInfo_;
            }

            private com.google.protobuf.MapField<String, String>
            internalGetMutableExtendInfo() {
                onChanged();
                ;
                if (extendInfo_ == null) {
                    extendInfo_ = com.google.protobuf.MapField.newMapField(
                            ExtendInfoDefaultEntryHolder.defaultEntry);
                }
                if (!extendInfo_.isMutable()) {
                    extendInfo_ = extendInfo_.copy();
                }
                return extendInfo_;
            }

            public int getExtendInfoCount() {
                return internalGetExtendInfo().getMap().size();
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 6;</code>
             */

            public boolean containsExtendInfo(
                    String key) {
                if (key == null) {
                    throw new NullPointerException();
                }
                return internalGetExtendInfo().getMap().containsKey(key);
            }

            /**
             * Use {@link #getExtendInfoMap()} instead.
             */
            @Deprecated
            public java.util.Map<String, String> getExtendInfo() {
                return getExtendInfoMap();
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 6;</code>
             */

            public java.util.Map<String, String> getExtendInfoMap() {
                return internalGetExtendInfo().getMap();
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 6;</code>
             */

            public String getExtendInfoOrDefault(
                    String key,
                    String defaultValue) {
                if (key == null) {
                    throw new NullPointerException();
                }
                java.util.Map<String, String> map =
                        internalGetExtendInfo().getMap();
                return map.containsKey(key) ? map.get(key) : defaultValue;
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 6;</code>
             */

            public String getExtendInfoOrThrow(
                    String key) {
                if (key == null) {
                    throw new NullPointerException();
                }
                java.util.Map<String, String> map =
                        internalGetExtendInfo().getMap();
                if (!map.containsKey(key)) {
                    throw new IllegalArgumentException();
                }
                return map.get(key);
            }

            public Builder clearExtendInfo() {
                getMutableExtendInfo().clear();
                return this;
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 6;</code>
             */

            public Builder removeExtendInfo(
                    String key) {
                if (key == null) {
                    throw new NullPointerException();
                }
                getMutableExtendInfo().remove(key);
                return this;
            }

            /**
             * Use alternate mutation accessors instead.
             */
            @Deprecated
            public java.util.Map<String, String>
            getMutableExtendInfo() {
                return internalGetMutableExtendInfo().getMutableMap();
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 6;</code>
             */
            public Builder putExtendInfo(
                    String key,
                    String value) {
                if (key == null) {
                    throw new NullPointerException();
                }
                if (value == null) {
                    throw new NullPointerException();
                }
                getMutableExtendInfo().put(key, value);
                return this;
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 6;</code>
             */

            public Builder putAllExtendInfo(
                    java.util.Map<String, String> values) {
                getMutableExtendInfo().putAll(values);
                return this;
            }

            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return this;
            }

            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return this;
            }


            // @@protoc_insertion_point(builder_scope:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsReq)
        }

        // @@protoc_insertion_point(class_scope:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsReq)
        private static final GetPublishInfoByUidsReq DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new GetPublishInfoByUidsReq();
        }

        public static GetPublishInfoByUidsReq getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<GetPublishInfoByUidsReq>
                PARSER = new com.google.protobuf.AbstractParser<GetPublishInfoByUidsReq>() {
            public GetPublishInfoByUidsReq parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return new GetPublishInfoByUidsReq(input, extensionRegistry);
            }
        };

        public static com.google.protobuf.Parser<GetPublishInfoByUidsReq> parser() {
            return PARSER;
        }

        @Override
        public com.google.protobuf.Parser<GetPublishInfoByUidsReq> getParserForType() {
            return PARSER;
        }

        public GetPublishInfoByUidsReq getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface GetPublishInfoByUidsRspOrBuilder extends
            // @@protoc_insertion_point(interface_extends:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsRsp)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * *
         * 返回结果：0 成功，1 失败，2 uid 列表参数为空，3 uid数量不能超过500
         * </pre>
         *
         * <code>optional uint32 result = 1;</code>
         */
        int getResult();

        /**
         * <pre>
         * *
         * 当前直播中数据
         * </pre>
         *
         * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
         */
        java.util.List<QueryLivingDataMap>
        getCurLiveListList();

        /**
         * <pre>
         * *
         * 当前直播中数据
         * </pre>
         *
         * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
         */
        QueryLivingDataMap getCurLiveList(int index);

        /**
         * <pre>
         * *
         * 当前直播中数据
         * </pre>
         *
         * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
         */
        int getCurLiveListCount();

        /**
         * <pre>
         * *
         * 当前直播中数据
         * </pre>
         *
         * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
         */
        java.util.List<? extends QueryLivingDataMapOrBuilder>
        getCurLiveListOrBuilderList();

        /**
         * <pre>
         * *
         * 当前直播中数据
         * </pre>
         *
         * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
         */
        QueryLivingDataMapOrBuilder getCurLiveListOrBuilder(
                int index);

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 3;</code>
         */
        int getExtendInfoCount();

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 3;</code>
         */
        boolean containsExtendInfo(
                String key);

        /**
         * Use {@link #getExtendInfoMap()} instead.
         */
        @Deprecated
        java.util.Map<String, String>
        getExtendInfo();

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 3;</code>
         */
        java.util.Map<String, String>
        getExtendInfoMap();

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 3;</code>
         */

        String getExtendInfoOrDefault(
                String key,
                String defaultValue);

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 3;</code>
         */

        String getExtendInfoOrThrow(
                String key);
    }

    /**
     * Protobuf type {@code com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsRsp}
     */
    public static final class GetPublishInfoByUidsRsp extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsRsp)
            GetPublishInfoByUidsRspOrBuilder {
        // Use GetPublishInfoByUidsRsp.newBuilder() to construct.
        private GetPublishInfoByUidsRsp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private GetPublishInfoByUidsRsp() {
            result_ = 0;
            curLiveList_ = java.util.Collections.emptyList();
        }

        @Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
            return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
        }

        private GetPublishInfoByUidsRsp(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            this();
            int mutable_bitField0_ = 0;
            try {
                boolean done = false;
                while (!done) {
                    int tag = input.readTag();
                    switch (tag) {
                        case 0:
                            done = true;
                            break;
                        default: {
                            if (!input.skipField(tag)) {
                                done = true;
                            }
                            break;
                        }
                        case 8: {

                            result_ = input.readUInt32();
                            break;
                        }
                        case 18: {
                            if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                                curLiveList_ = new java.util.ArrayList<QueryLivingDataMap>();
                                mutable_bitField0_ |= 0x00000002;
                            }
                            curLiveList_.add(
                                    input.readMessage(QueryLivingDataMap.parser(), extensionRegistry));
                            break;
                        }
                        case 26: {
                            if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                                extendInfo_ = com.google.protobuf.MapField.newMapField(
                                        ExtendInfoDefaultEntryHolder.defaultEntry);
                                mutable_bitField0_ |= 0x00000004;
                            }
                            com.google.protobuf.MapEntry<String, String>
                                    extendInfo__ = input.readMessage(
                                    ExtendInfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
                            extendInfo_.getMutableMap().put(
                                    extendInfo__.getKey(), extendInfo__.getValue());
                            break;
                        }
                    }
                }
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.setUnfinishedMessage(this);
            } catch (java.io.IOException e) {
                throw new com.google.protobuf.InvalidProtocolBufferException(
                        e).setUnfinishedMessage(this);
            } finally {
                if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                    curLiveList_ = java.util.Collections.unmodifiableList(curLiveList_);
                }
                makeExtensionsImmutable();
            }
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_descriptor;
        }

        @SuppressWarnings({"rawtypes"})
        protected com.google.protobuf.MapField internalGetMapField(
                int number) {
            switch (number) {
                case 3:
                    return internalGetExtendInfo();
                default:
                    throw new RuntimeException(
                            "Invalid map field number: " + number);
            }
        }

        protected FieldAccessorTable
        internalGetFieldAccessorTable() {
            return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            GetPublishInfoByUidsRsp.class, Builder.class);
        }

        private int bitField0_;
        public static final int RESULT_FIELD_NUMBER = 1;
        private int result_;

        /**
         * <pre>
         * *
         * 返回结果：0 成功，1 失败，2 uid 列表参数为空，3 uid数量不能超过500
         * </pre>
         *
         * <code>optional uint32 result = 1;</code>
         */
        public int getResult() {
            return result_;
        }

        public static final int CURLIVELIST_FIELD_NUMBER = 2;
        private java.util.List<QueryLivingDataMap> curLiveList_;

        /**
         * <pre>
         * *
         * 当前直播中数据
         * </pre>
         *
         * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
         */
        public java.util.List<QueryLivingDataMap> getCurLiveListList() {
            return curLiveList_;
        }

        /**
         * <pre>
         * *
         * 当前直播中数据
         * </pre>
         *
         * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
         */
        public java.util.List<? extends QueryLivingDataMapOrBuilder>
        getCurLiveListOrBuilderList() {
            return curLiveList_;
        }

        /**
         * <pre>
         * *
         * 当前直播中数据
         * </pre>
         *
         * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
         */
        public int getCurLiveListCount() {
            return curLiveList_.size();
        }

        /**
         * <pre>
         * *
         * 当前直播中数据
         * </pre>
         *
         * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
         */
        public QueryLivingDataMap getCurLiveList(int index) {
            return curLiveList_.get(index);
        }

        /**
         * <pre>
         * *
         * 当前直播中数据
         * </pre>
         *
         * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
         */
        public QueryLivingDataMapOrBuilder getCurLiveListOrBuilder(
                int index) {
            return curLiveList_.get(index);
        }

        public static final int EXTENDINFO_FIELD_NUMBER = 3;

        private static final class ExtendInfoDefaultEntryHolder {
            static final com.google.protobuf.MapEntry<
                    String, String> defaultEntry =
                    com.google.protobuf.MapEntry
                            .<String, String>newDefaultInstance(
                                    lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_ExtendInfoEntry_descriptor,
                                    com.google.protobuf.WireFormat.FieldType.STRING,
                                    "",
                                    com.google.protobuf.WireFormat.FieldType.STRING,
                                    "");
        }

        private com.google.protobuf.MapField<
                String, String> extendInfo_;

        private com.google.protobuf.MapField<String, String>
        internalGetExtendInfo() {
            if (extendInfo_ == null) {
                return com.google.protobuf.MapField.emptyMapField(
                        ExtendInfoDefaultEntryHolder.defaultEntry);
            }
            return extendInfo_;
        }

        public int getExtendInfoCount() {
            return internalGetExtendInfo().getMap().size();
        }

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 3;</code>
         */

        public boolean containsExtendInfo(
                String key) {
            if (key == null) {
                throw new NullPointerException();
            }
            return internalGetExtendInfo().getMap().containsKey(key);
        }

        /**
         * Use {@link #getExtendInfoMap()} instead.
         */
        @Deprecated
        public java.util.Map<String, String> getExtendInfo() {
            return getExtendInfoMap();
        }

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 3;</code>
         */

        public java.util.Map<String, String> getExtendInfoMap() {
            return internalGetExtendInfo().getMap();
        }

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 3;</code>
         */

        public String getExtendInfoOrDefault(
                String key,
                String defaultValue) {
            if (key == null) {
                throw new NullPointerException();
            }
            java.util.Map<String, String> map =
                    internalGetExtendInfo().getMap();
            return map.containsKey(key) ? map.get(key) : defaultValue;
        }

        /**
         * <pre>
         * *
         * 扩展信息
         * </pre>
         *
         * <code>map&lt;string, string&gt; extendInfo = 3;</code>
         */

        public String getExtendInfoOrThrow(
                String key) {
            if (key == null) {
                throw new NullPointerException();
            }
            java.util.Map<String, String> map =
                    internalGetExtendInfo().getMap();
            if (!map.containsKey(key)) {
                throw new IllegalArgumentException();
            }
            return map.get(key);
        }

        private byte memoizedIsInitialized = -1;

        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (result_ != 0) {
                output.writeUInt32(1, result_);
            }
            for (int i = 0; i < curLiveList_.size(); i++) {
                output.writeMessage(2, curLiveList_.get(i));
            }
            com.google.protobuf.GeneratedMessageV3
                    .serializeStringMapTo(
                            output,
                            internalGetExtendInfo(),
                            ExtendInfoDefaultEntryHolder.defaultEntry,
                            3);
        }

        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (result_ != 0) {
                size += com.google.protobuf.CodedOutputStream
                        .computeUInt32Size(1, result_);
            }
            for (int i = 0; i < curLiveList_.size(); i++) {
                size += com.google.protobuf.CodedOutputStream
                        .computeMessageSize(2, curLiveList_.get(i));
            }
            for (java.util.Map.Entry<String, String> entry
                    : internalGetExtendInfo().getMap().entrySet()) {
                com.google.protobuf.MapEntry<String, String>
                        extendInfo__ = ExtendInfoDefaultEntryHolder.defaultEntry.newBuilderForType()
                        .setKey(entry.getKey())
                        .setValue(entry.getValue())
                        .build();
                size += com.google.protobuf.CodedOutputStream
                        .computeMessageSize(3, extendInfo__);
            }
            memoizedSize = size;
            return size;
        }

        private static final long serialVersionUID = 0L;

        @Override
        public boolean equals(final Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof GetPublishInfoByUidsRsp)) {
                return super.equals(obj);
            }
            GetPublishInfoByUidsRsp other = (GetPublishInfoByUidsRsp) obj;

            boolean result = true;
            result = result && (getResult()
                    == other.getResult());
            result = result && getCurLiveListList()
                    .equals(other.getCurLiveListList());
            result = result && internalGetExtendInfo().equals(
                    other.internalGetExtendInfo());
            return result;
        }

        @Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptorForType().hashCode();
            hash = (37 * hash) + RESULT_FIELD_NUMBER;
            hash = (53 * hash) + getResult();
            if (getCurLiveListCount() > 0) {
                hash = (37 * hash) + CURLIVELIST_FIELD_NUMBER;
                hash = (53 * hash) + getCurLiveListList().hashCode();
            }
            if (!internalGetExtendInfo().getMap().isEmpty()) {
                hash = (37 * hash) + EXTENDINFO_FIELD_NUMBER;
                hash = (53 * hash) + internalGetExtendInfo().hashCode();
            }
            hash = (29 * hash) + unknownFields.hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static GetPublishInfoByUidsRsp parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static GetPublishInfoByUidsRsp parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static GetPublishInfoByUidsRsp parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static GetPublishInfoByUidsRsp parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static GetPublishInfoByUidsRsp parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static GetPublishInfoByUidsRsp parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static GetPublishInfoByUidsRsp parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static GetPublishInfoByUidsRsp parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static GetPublishInfoByUidsRsp parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static GetPublishInfoByUidsRsp parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(GetPublishInfoByUidsRsp prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @Override
        protected Builder newBuilderForType(
                BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsRsp}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsRsp)
                GetPublishInfoByUidsRspOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_descriptor;
            }

            @SuppressWarnings({"rawtypes"})
            protected com.google.protobuf.MapField internalGetMapField(
                    int number) {
                switch (number) {
                    case 3:
                        return internalGetExtendInfo();
                    default:
                        throw new RuntimeException(
                                "Invalid map field number: " + number);
                }
            }

            @SuppressWarnings({"rawtypes"})
            protected com.google.protobuf.MapField internalGetMutableMapField(
                    int number) {
                switch (number) {
                    case 3:
                        return internalGetMutableExtendInfo();
                    default:
                        throw new RuntimeException(
                                "Invalid map field number: " + number);
                }
            }

            protected FieldAccessorTable
            internalGetFieldAccessorTable() {
                return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                GetPublishInfoByUidsRsp.class, Builder.class);
            }

            // Construct using com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.lpfm2YYP.GetPublishInfoByUidsRsp.newBuilder()
            private Builder() {
                maybeForceBuilderInitialization();
            }

            private Builder(
                    BuilderParent parent) {
                super(parent);
                maybeForceBuilderInitialization();
            }

            private void maybeForceBuilderInitialization() {
                if (com.google.protobuf.GeneratedMessageV3
                        .alwaysUseFieldBuilders) {
                    getCurLiveListFieldBuilder();
                }
            }

            public Builder clear() {
                super.clear();
                result_ = 0;

                if (curLiveListBuilder_ == null) {
                    curLiveList_ = java.util.Collections.emptyList();
                    bitField0_ = (bitField0_ & ~0x00000002);
                } else {
                    curLiveListBuilder_.clear();
                }
                internalGetMutableExtendInfo().clear();
                return this;
            }

            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_descriptor;
            }

            public GetPublishInfoByUidsRsp getDefaultInstanceForType() {
                return GetPublishInfoByUidsRsp.getDefaultInstance();
            }

            public GetPublishInfoByUidsRsp build() {
                GetPublishInfoByUidsRsp result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            public GetPublishInfoByUidsRsp buildPartial() {
                GetPublishInfoByUidsRsp result = new GetPublishInfoByUidsRsp(this);
                int from_bitField0_ = bitField0_;
                int to_bitField0_ = 0;
                result.result_ = result_;
                if (curLiveListBuilder_ == null) {
                    if (((bitField0_ & 0x00000002) == 0x00000002)) {
                        curLiveList_ = java.util.Collections.unmodifiableList(curLiveList_);
                        bitField0_ = (bitField0_ & ~0x00000002);
                    }
                    result.curLiveList_ = curLiveList_;
                } else {
                    result.curLiveList_ = curLiveListBuilder_.build();
                }
                result.extendInfo_ = internalGetExtendInfo();
                result.extendInfo_.makeImmutable();
                result.bitField0_ = to_bitField0_;
                onBuilt();
                return result;
            }

            public Builder clone() {
                return (Builder) super.clone();
            }

            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    Object value) {
                return (Builder) super.setField(field, value);
            }

            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return (Builder) super.clearField(field);
            }

            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return (Builder) super.clearOneof(oneof);
            }

            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, Object value) {
                return (Builder) super.setRepeatedField(field, index, value);
            }

            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    Object value) {
                return (Builder) super.addRepeatedField(field, value);
            }

            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof GetPublishInfoByUidsRsp) {
                    return mergeFrom((GetPublishInfoByUidsRsp) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(GetPublishInfoByUidsRsp other) {
                if (other == GetPublishInfoByUidsRsp.getDefaultInstance())
                    return this;
                if (other.getResult() != 0) {
                    setResult(other.getResult());
                }
                if (curLiveListBuilder_ == null) {
                    if (!other.curLiveList_.isEmpty()) {
                        if (curLiveList_.isEmpty()) {
                            curLiveList_ = other.curLiveList_;
                            bitField0_ = (bitField0_ & ~0x00000002);
                        } else {
                            ensureCurLiveListIsMutable();
                            curLiveList_.addAll(other.curLiveList_);
                        }
                        onChanged();
                    }
                } else {
                    if (!other.curLiveList_.isEmpty()) {
                        if (curLiveListBuilder_.isEmpty()) {
                            curLiveListBuilder_.dispose();
                            curLiveListBuilder_ = null;
                            curLiveList_ = other.curLiveList_;
                            bitField0_ = (bitField0_ & ~0x00000002);
                            curLiveListBuilder_ =
                                    com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                                            getCurLiveListFieldBuilder() : null;
                        } else {
                            curLiveListBuilder_.addAllMessages(other.curLiveList_);
                        }
                    }
                }
                internalGetMutableExtendInfo().mergeFrom(
                        other.internalGetExtendInfo());
                onChanged();
                return this;
            }

            public final boolean isInitialized() {
                return true;
            }

            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                GetPublishInfoByUidsRsp parsedMessage = null;
                try {
                    parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    parsedMessage = (GetPublishInfoByUidsRsp) e.getUnfinishedMessage();
                    throw e.unwrapIOException();
                } finally {
                    if (parsedMessage != null) {
                        mergeFrom(parsedMessage);
                    }
                }
                return this;
            }

            private int bitField0_;

            private int result_;

            /**
             * <pre>
             * *
             * 返回结果：0 成功，1 失败，2 uid 列表参数为空，3 uid数量不能超过500
             * </pre>
             *
             * <code>optional uint32 result = 1;</code>
             */
            public int getResult() {
                return result_;
            }

            /**
             * <pre>
             * *
             * 返回结果：0 成功，1 失败，2 uid 列表参数为空，3 uid数量不能超过500
             * </pre>
             *
             * <code>optional uint32 result = 1;</code>
             */
            public Builder setResult(int value) {

                result_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * *
             * 返回结果：0 成功，1 失败，2 uid 列表参数为空，3 uid数量不能超过500
             * </pre>
             *
             * <code>optional uint32 result = 1;</code>
             */
            public Builder clearResult() {

                result_ = 0;
                onChanged();
                return this;
            }

            private java.util.List<QueryLivingDataMap> curLiveList_ =
                    java.util.Collections.emptyList();

            private void ensureCurLiveListIsMutable() {
                if (!((bitField0_ & 0x00000002) == 0x00000002)) {
                    curLiveList_ = new java.util.ArrayList<QueryLivingDataMap>(curLiveList_);
                    bitField0_ |= 0x00000002;
                }
            }

            private com.google.protobuf.RepeatedFieldBuilderV3<
                    QueryLivingDataMap, QueryLivingDataMap.Builder, QueryLivingDataMapOrBuilder> curLiveListBuilder_;

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public java.util.List<QueryLivingDataMap> getCurLiveListList() {
                if (curLiveListBuilder_ == null) {
                    return java.util.Collections.unmodifiableList(curLiveList_);
                } else {
                    return curLiveListBuilder_.getMessageList();
                }
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public int getCurLiveListCount() {
                if (curLiveListBuilder_ == null) {
                    return curLiveList_.size();
                } else {
                    return curLiveListBuilder_.getCount();
                }
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public QueryLivingDataMap getCurLiveList(int index) {
                if (curLiveListBuilder_ == null) {
                    return curLiveList_.get(index);
                } else {
                    return curLiveListBuilder_.getMessage(index);
                }
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public Builder setCurLiveList(
                    int index, QueryLivingDataMap value) {
                if (curLiveListBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureCurLiveListIsMutable();
                    curLiveList_.set(index, value);
                    onChanged();
                } else {
                    curLiveListBuilder_.setMessage(index, value);
                }
                return this;
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public Builder setCurLiveList(
                    int index, QueryLivingDataMap.Builder builderForValue) {
                if (curLiveListBuilder_ == null) {
                    ensureCurLiveListIsMutable();
                    curLiveList_.set(index, builderForValue.build());
                    onChanged();
                } else {
                    curLiveListBuilder_.setMessage(index, builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public Builder addCurLiveList(QueryLivingDataMap value) {
                if (curLiveListBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureCurLiveListIsMutable();
                    curLiveList_.add(value);
                    onChanged();
                } else {
                    curLiveListBuilder_.addMessage(value);
                }
                return this;
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public Builder addCurLiveList(
                    int index, QueryLivingDataMap value) {
                if (curLiveListBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureCurLiveListIsMutable();
                    curLiveList_.add(index, value);
                    onChanged();
                } else {
                    curLiveListBuilder_.addMessage(index, value);
                }
                return this;
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public Builder addCurLiveList(
                    QueryLivingDataMap.Builder builderForValue) {
                if (curLiveListBuilder_ == null) {
                    ensureCurLiveListIsMutable();
                    curLiveList_.add(builderForValue.build());
                    onChanged();
                } else {
                    curLiveListBuilder_.addMessage(builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public Builder addCurLiveList(
                    int index, QueryLivingDataMap.Builder builderForValue) {
                if (curLiveListBuilder_ == null) {
                    ensureCurLiveListIsMutable();
                    curLiveList_.add(index, builderForValue.build());
                    onChanged();
                } else {
                    curLiveListBuilder_.addMessage(index, builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public Builder addAllCurLiveList(
                    Iterable<? extends QueryLivingDataMap> values) {
                if (curLiveListBuilder_ == null) {
                    ensureCurLiveListIsMutable();
                    com.google.protobuf.AbstractMessageLite.Builder.addAll(
                            values, curLiveList_);
                    onChanged();
                } else {
                    curLiveListBuilder_.addAllMessages(values);
                }
                return this;
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public Builder clearCurLiveList() {
                if (curLiveListBuilder_ == null) {
                    curLiveList_ = java.util.Collections.emptyList();
                    bitField0_ = (bitField0_ & ~0x00000002);
                    onChanged();
                } else {
                    curLiveListBuilder_.clear();
                }
                return this;
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public Builder removeCurLiveList(int index) {
                if (curLiveListBuilder_ == null) {
                    ensureCurLiveListIsMutable();
                    curLiveList_.remove(index);
                    onChanged();
                } else {
                    curLiveListBuilder_.remove(index);
                }
                return this;
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public QueryLivingDataMap.Builder getCurLiveListBuilder(
                    int index) {
                return getCurLiveListFieldBuilder().getBuilder(index);
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public QueryLivingDataMapOrBuilder getCurLiveListOrBuilder(
                    int index) {
                if (curLiveListBuilder_ == null) {
                    return curLiveList_.get(index);
                } else {
                    return curLiveListBuilder_.getMessageOrBuilder(index);
                }
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public java.util.List<? extends QueryLivingDataMapOrBuilder>
            getCurLiveListOrBuilderList() {
                if (curLiveListBuilder_ != null) {
                    return curLiveListBuilder_.getMessageOrBuilderList();
                } else {
                    return java.util.Collections.unmodifiableList(curLiveList_);
                }
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public QueryLivingDataMap.Builder addCurLiveListBuilder() {
                return getCurLiveListFieldBuilder().addBuilder(
                        QueryLivingDataMap.getDefaultInstance());
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public QueryLivingDataMap.Builder addCurLiveListBuilder(
                    int index) {
                return getCurLiveListFieldBuilder().addBuilder(
                        index, QueryLivingDataMap.getDefaultInstance());
            }

            /**
             * <pre>
             * *
             * 当前直播中数据
             * </pre>
             *
             * <code>repeated .com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap curLiveList = 2;</code>
             */
            public java.util.List<QueryLivingDataMap.Builder>
            getCurLiveListBuilderList() {
                return getCurLiveListFieldBuilder().getBuilderList();
            }

            private com.google.protobuf.RepeatedFieldBuilderV3<
                    QueryLivingDataMap, QueryLivingDataMap.Builder, QueryLivingDataMapOrBuilder>
            getCurLiveListFieldBuilder() {
                if (curLiveListBuilder_ == null) {
                    curLiveListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                            QueryLivingDataMap, QueryLivingDataMap.Builder, QueryLivingDataMapOrBuilder>(
                            curLiveList_,
                            ((bitField0_ & 0x00000002) == 0x00000002),
                            getParentForChildren(),
                            isClean());
                    curLiveList_ = null;
                }
                return curLiveListBuilder_;
            }

            private com.google.protobuf.MapField<
                    String, String> extendInfo_;

            private com.google.protobuf.MapField<String, String>
            internalGetExtendInfo() {
                if (extendInfo_ == null) {
                    return com.google.protobuf.MapField.emptyMapField(
                            ExtendInfoDefaultEntryHolder.defaultEntry);
                }
                return extendInfo_;
            }

            private com.google.protobuf.MapField<String, String>
            internalGetMutableExtendInfo() {
                onChanged();
                ;
                if (extendInfo_ == null) {
                    extendInfo_ = com.google.protobuf.MapField.newMapField(
                            ExtendInfoDefaultEntryHolder.defaultEntry);
                }
                if (!extendInfo_.isMutable()) {
                    extendInfo_ = extendInfo_.copy();
                }
                return extendInfo_;
            }

            public int getExtendInfoCount() {
                return internalGetExtendInfo().getMap().size();
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 3;</code>
             */

            public boolean containsExtendInfo(
                    String key) {
                if (key == null) {
                    throw new NullPointerException();
                }
                return internalGetExtendInfo().getMap().containsKey(key);
            }

            /**
             * Use {@link #getExtendInfoMap()} instead.
             */
            @Deprecated
            public java.util.Map<String, String> getExtendInfo() {
                return getExtendInfoMap();
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 3;</code>
             */

            public java.util.Map<String, String> getExtendInfoMap() {
                return internalGetExtendInfo().getMap();
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 3;</code>
             */

            public String getExtendInfoOrDefault(
                    String key,
                    String defaultValue) {
                if (key == null) {
                    throw new NullPointerException();
                }
                java.util.Map<String, String> map =
                        internalGetExtendInfo().getMap();
                return map.containsKey(key) ? map.get(key) : defaultValue;
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 3;</code>
             */

            public String getExtendInfoOrThrow(
                    String key) {
                if (key == null) {
                    throw new NullPointerException();
                }
                java.util.Map<String, String> map =
                        internalGetExtendInfo().getMap();
                if (!map.containsKey(key)) {
                    throw new IllegalArgumentException();
                }
                return map.get(key);
            }

            public Builder clearExtendInfo() {
                getMutableExtendInfo().clear();
                return this;
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 3;</code>
             */

            public Builder removeExtendInfo(
                    String key) {
                if (key == null) {
                    throw new NullPointerException();
                }
                getMutableExtendInfo().remove(key);
                return this;
            }

            /**
             * Use alternate mutation accessors instead.
             */
            @Deprecated
            public java.util.Map<String, String>
            getMutableExtendInfo() {
                return internalGetMutableExtendInfo().getMutableMap();
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 3;</code>
             */
            public Builder putExtendInfo(
                    String key,
                    String value) {
                if (key == null) {
                    throw new NullPointerException();
                }
                if (value == null) {
                    throw new NullPointerException();
                }
                getMutableExtendInfo().put(key, value);
                return this;
            }

            /**
             * <pre>
             * *
             * 扩展信息
             * </pre>
             *
             * <code>map&lt;string, string&gt; extendInfo = 3;</code>
             */

            public Builder putAllExtendInfo(
                    java.util.Map<String, String> values) {
                getMutableExtendInfo().putAll(values);
                return this;
            }

            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return this;
            }

            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return this;
            }


            // @@protoc_insertion_point(builder_scope:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsRsp)
        }

        // @@protoc_insertion_point(class_scope:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.GetPublishInfoByUidsRsp)
        private static final GetPublishInfoByUidsRsp DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new GetPublishInfoByUidsRsp();
        }

        public static GetPublishInfoByUidsRsp getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<GetPublishInfoByUidsRsp>
                PARSER = new com.google.protobuf.AbstractParser<GetPublishInfoByUidsRsp>() {
            public GetPublishInfoByUidsRsp parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return new GetPublishInfoByUidsRsp(input, extensionRegistry);
            }
        };

        public static com.google.protobuf.Parser<GetPublishInfoByUidsRsp> parser() {
            return PARSER;
        }

        @Override
        public com.google.protobuf.Parser<GetPublishInfoByUidsRsp> getParserForType() {
            return PARSER;
        }

        public GetPublishInfoByUidsRsp getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface QueryLivingDataMapOrBuilder extends
            // @@protoc_insertion_point(interface_extends:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * *
         * Map 的 key
         * uid uid
         * sid 顶级频道
         * ssid 子频道
         * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
         * </pre>
         *
         * <code>map&lt;string, string&gt; extend = 1;</code>
         */
        int getExtendCount();

        /**
         * <pre>
         * *
         * Map 的 key
         * uid uid
         * sid 顶级频道
         * ssid 子频道
         * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
         * </pre>
         *
         * <code>map&lt;string, string&gt; extend = 1;</code>
         */
        boolean containsExtend(
                String key);

        /**
         * Use {@link #getExtendMap()} instead.
         */
        @Deprecated
        java.util.Map<String, String>
        getExtend();

        /**
         * <pre>
         * *
         * Map 的 key
         * uid uid
         * sid 顶级频道
         * ssid 子频道
         * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
         * </pre>
         *
         * <code>map&lt;string, string&gt; extend = 1;</code>
         */
        java.util.Map<String, String>
        getExtendMap();

        /**
         * <pre>
         * *
         * Map 的 key
         * uid uid
         * sid 顶级频道
         * ssid 子频道
         * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
         * </pre>
         *
         * <code>map&lt;string, string&gt; extend = 1;</code>
         */

        String getExtendOrDefault(
                String key,
                String defaultValue);

        /**
         * <pre>
         * *
         * Map 的 key
         * uid uid
         * sid 顶级频道
         * ssid 子频道
         * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
         * </pre>
         *
         * <code>map&lt;string, string&gt; extend = 1;</code>
         */

        String getExtendOrThrow(
                String key);
    }

    /**
     * Protobuf type {@code com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap}
     */
    public static final class QueryLivingDataMap extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap)
            QueryLivingDataMapOrBuilder {
        // Use QueryLivingDataMap.newBuilder() to construct.
        private QueryLivingDataMap(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private QueryLivingDataMap() {
        }

        @Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
            return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
        }

        private QueryLivingDataMap(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            this();
            int mutable_bitField0_ = 0;
            try {
                boolean done = false;
                while (!done) {
                    int tag = input.readTag();
                    switch (tag) {
                        case 0:
                            done = true;
                            break;
                        default: {
                            if (!input.skipField(tag)) {
                                done = true;
                            }
                            break;
                        }
                        case 10: {
                            if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                                extend_ = com.google.protobuf.MapField.newMapField(
                                        ExtendDefaultEntryHolder.defaultEntry);
                                mutable_bitField0_ |= 0x00000001;
                            }
                            com.google.protobuf.MapEntry<String, String>
                                    extend__ = input.readMessage(
                                    ExtendDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
                            extend_.getMutableMap().put(
                                    extend__.getKey(), extend__.getValue());
                            break;
                        }
                    }
                }
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                throw e.setUnfinishedMessage(this);
            } catch (java.io.IOException e) {
                throw new com.google.protobuf.InvalidProtocolBufferException(
                        e).setUnfinishedMessage(this);
            } finally {
                makeExtensionsImmutable();
            }
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_descriptor;
        }

        @SuppressWarnings({"rawtypes"})
        protected com.google.protobuf.MapField internalGetMapField(
                int number) {
            switch (number) {
                case 1:
                    return internalGetExtend();
                default:
                    throw new RuntimeException(
                            "Invalid map field number: " + number);
            }
        }

        protected FieldAccessorTable
        internalGetFieldAccessorTable() {
            return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            QueryLivingDataMap.class, Builder.class);
        }

        public static final int EXTEND_FIELD_NUMBER = 1;

        private static final class ExtendDefaultEntryHolder {
            static final com.google.protobuf.MapEntry<
                    String, String> defaultEntry =
                    com.google.protobuf.MapEntry
                            .<String, String>newDefaultInstance(
                                    lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_ExtendEntry_descriptor,
                                    com.google.protobuf.WireFormat.FieldType.STRING,
                                    "",
                                    com.google.protobuf.WireFormat.FieldType.STRING,
                                    "");
        }

        private com.google.protobuf.MapField<
                String, String> extend_;

        private com.google.protobuf.MapField<String, String>
        internalGetExtend() {
            if (extend_ == null) {
                return com.google.protobuf.MapField.emptyMapField(
                        ExtendDefaultEntryHolder.defaultEntry);
            }
            return extend_;
        }

        public int getExtendCount() {
            return internalGetExtend().getMap().size();
        }

        /**
         * <pre>
         * *
         * Map 的 key
         * uid uid
         * sid 顶级频道
         * ssid 子频道
         * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
         * </pre>
         *
         * <code>map&lt;string, string&gt; extend = 1;</code>
         */

        public boolean containsExtend(
                String key) {
            if (key == null) {
                throw new NullPointerException();
            }
            return internalGetExtend().getMap().containsKey(key);
        }

        /**
         * Use {@link #getExtendMap()} instead.
         */
        @Deprecated
        public java.util.Map<String, String> getExtend() {
            return getExtendMap();
        }

        /**
         * <pre>
         * *
         * Map 的 key
         * uid uid
         * sid 顶级频道
         * ssid 子频道
         * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
         * </pre>
         *
         * <code>map&lt;string, string&gt; extend = 1;</code>
         */

        public java.util.Map<String, String> getExtendMap() {
            return internalGetExtend().getMap();
        }

        /**
         * <pre>
         * *
         * Map 的 key
         * uid uid
         * sid 顶级频道
         * ssid 子频道
         * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
         * </pre>
         *
         * <code>map&lt;string, string&gt; extend = 1;</code>
         */

        public String getExtendOrDefault(
                String key,
                String defaultValue) {
            if (key == null) {
                throw new NullPointerException();
            }
            java.util.Map<String, String> map =
                    internalGetExtend().getMap();
            return map.containsKey(key) ? map.get(key) : defaultValue;
        }

        /**
         * <pre>
         * *
         * Map 的 key
         * uid uid
         * sid 顶级频道
         * ssid 子频道
         * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
         * </pre>
         *
         * <code>map&lt;string, string&gt; extend = 1;</code>
         */

        public String getExtendOrThrow(
                String key) {
            if (key == null) {
                throw new NullPointerException();
            }
            java.util.Map<String, String> map =
                    internalGetExtend().getMap();
            if (!map.containsKey(key)) {
                throw new IllegalArgumentException();
            }
            return map.get(key);
        }

        private byte memoizedIsInitialized = -1;

        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            com.google.protobuf.GeneratedMessageV3
                    .serializeStringMapTo(
                            output,
                            internalGetExtend(),
                            ExtendDefaultEntryHolder.defaultEntry,
                            1);
        }

        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            for (java.util.Map.Entry<String, String> entry
                    : internalGetExtend().getMap().entrySet()) {
                com.google.protobuf.MapEntry<String, String>
                        extend__ = ExtendDefaultEntryHolder.defaultEntry.newBuilderForType()
                        .setKey(entry.getKey())
                        .setValue(entry.getValue())
                        .build();
                size += com.google.protobuf.CodedOutputStream
                        .computeMessageSize(1, extend__);
            }
            memoizedSize = size;
            return size;
        }

        private static final long serialVersionUID = 0L;

        @Override
        public boolean equals(final Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof QueryLivingDataMap)) {
                return super.equals(obj);
            }
            QueryLivingDataMap other = (QueryLivingDataMap) obj;

            boolean result = true;
            result = result && internalGetExtend().equals(
                    other.internalGetExtend());
            return result;
        }

        @Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptorForType().hashCode();
            if (!internalGetExtend().getMap().isEmpty()) {
                hash = (37 * hash) + EXTEND_FIELD_NUMBER;
                hash = (53 * hash) + internalGetExtend().hashCode();
            }
            hash = (29 * hash) + unknownFields.hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static QueryLivingDataMap parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static QueryLivingDataMap parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static QueryLivingDataMap parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static QueryLivingDataMap parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static QueryLivingDataMap parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static QueryLivingDataMap parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static QueryLivingDataMap parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static QueryLivingDataMap parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static QueryLivingDataMap parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static QueryLivingDataMap parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(QueryLivingDataMap prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @Override
        protected Builder newBuilderForType(
                BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap)
                QueryLivingDataMapOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_descriptor;
            }

            @SuppressWarnings({"rawtypes"})
            protected com.google.protobuf.MapField internalGetMapField(
                    int number) {
                switch (number) {
                    case 1:
                        return internalGetExtend();
                    default:
                        throw new RuntimeException(
                                "Invalid map field number: " + number);
                }
            }

            @SuppressWarnings({"rawtypes"})
            protected com.google.protobuf.MapField internalGetMutableMapField(
                    int number) {
                switch (number) {
                    case 1:
                        return internalGetMutableExtend();
                    default:
                        throw new RuntimeException(
                                "Invalid map field number: " + number);
                }
            }

            protected FieldAccessorTable
            internalGetFieldAccessorTable() {
                return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                QueryLivingDataMap.class, Builder.class);
            }

            // Construct using com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.lpfm2YYP.QueryLivingDataMap.newBuilder()
            private Builder() {
                maybeForceBuilderInitialization();
            }

            private Builder(
                    BuilderParent parent) {
                super(parent);
                maybeForceBuilderInitialization();
            }

            private void maybeForceBuilderInitialization() {
                if (com.google.protobuf.GeneratedMessageV3
                        .alwaysUseFieldBuilders) {
                }
            }

            public Builder clear() {
                super.clear();
                internalGetMutableExtend().clear();
                return this;
            }

            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return lpfm2YYP.internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_descriptor;
            }

            public QueryLivingDataMap getDefaultInstanceForType() {
                return QueryLivingDataMap.getDefaultInstance();
            }

            public QueryLivingDataMap build() {
                QueryLivingDataMap result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            public QueryLivingDataMap buildPartial() {
                QueryLivingDataMap result = new QueryLivingDataMap(this);
                int from_bitField0_ = bitField0_;
                result.extend_ = internalGetExtend();
                result.extend_.makeImmutable();
                onBuilt();
                return result;
            }

            public Builder clone() {
                return (Builder) super.clone();
            }

            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    Object value) {
                return (Builder) super.setField(field, value);
            }

            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return (Builder) super.clearField(field);
            }

            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return (Builder) super.clearOneof(oneof);
            }

            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, Object value) {
                return (Builder) super.setRepeatedField(field, index, value);
            }

            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    Object value) {
                return (Builder) super.addRepeatedField(field, value);
            }

            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof QueryLivingDataMap) {
                    return mergeFrom((QueryLivingDataMap) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(QueryLivingDataMap other) {
                if (other == QueryLivingDataMap.getDefaultInstance())
                    return this;
                internalGetMutableExtend().mergeFrom(
                        other.internalGetExtend());
                onChanged();
                return this;
            }

            public final boolean isInitialized() {
                return true;
            }

            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                QueryLivingDataMap parsedMessage = null;
                try {
                    parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    parsedMessage = (QueryLivingDataMap) e.getUnfinishedMessage();
                    throw e.unwrapIOException();
                } finally {
                    if (parsedMessage != null) {
                        mergeFrom(parsedMessage);
                    }
                }
                return this;
            }

            private int bitField0_;

            private com.google.protobuf.MapField<
                    String, String> extend_;

            private com.google.protobuf.MapField<String, String>
            internalGetExtend() {
                if (extend_ == null) {
                    return com.google.protobuf.MapField.emptyMapField(
                            ExtendDefaultEntryHolder.defaultEntry);
                }
                return extend_;
            }

            private com.google.protobuf.MapField<String, String>
            internalGetMutableExtend() {
                onChanged();
                ;
                if (extend_ == null) {
                    extend_ = com.google.protobuf.MapField.newMapField(
                            ExtendDefaultEntryHolder.defaultEntry);
                }
                if (!extend_.isMutable()) {
                    extend_ = extend_.copy();
                }
                return extend_;
            }

            public int getExtendCount() {
                return internalGetExtend().getMap().size();
            }

            /**
             * <pre>
             * *
             * Map 的 key
             * uid uid
             * sid 顶级频道
             * ssid 子频道
             * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
             * </pre>
             *
             * <code>map&lt;string, string&gt; extend = 1;</code>
             */

            public boolean containsExtend(
                    String key) {
                if (key == null) {
                    throw new NullPointerException();
                }
                return internalGetExtend().getMap().containsKey(key);
            }

            /**
             * Use {@link #getExtendMap()} instead.
             */
            @Deprecated
            public java.util.Map<String, String> getExtend() {
                return getExtendMap();
            }

            /**
             * <pre>
             * *
             * Map 的 key
             * uid uid
             * sid 顶级频道
             * ssid 子频道
             * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
             * </pre>
             *
             * <code>map&lt;string, string&gt; extend = 1;</code>
             */

            public java.util.Map<String, String> getExtendMap() {
                return internalGetExtend().getMap();
            }

            /**
             * <pre>
             * *
             * Map 的 key
             * uid uid
             * sid 顶级频道
             * ssid 子频道
             * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
             * </pre>
             *
             * <code>map&lt;string, string&gt; extend = 1;</code>
             */

            public String getExtendOrDefault(
                    String key,
                    String defaultValue) {
                if (key == null) {
                    throw new NullPointerException();
                }
                java.util.Map<String, String> map =
                        internalGetExtend().getMap();
                return map.containsKey(key) ? map.get(key) : defaultValue;
            }

            /**
             * <pre>
             * *
             * Map 的 key
             * uid uid
             * sid 顶级频道
             * ssid 子频道
             * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
             * </pre>
             *
             * <code>map&lt;string, string&gt; extend = 1;</code>
             */

            public String getExtendOrThrow(
                    String key) {
                if (key == null) {
                    throw new NullPointerException();
                }
                java.util.Map<String, String> map =
                        internalGetExtend().getMap();
                if (!map.containsKey(key)) {
                    throw new IllegalArgumentException();
                }
                return map.get(key);
            }

            public Builder clearExtend() {
                getMutableExtend().clear();
                return this;
            }

            /**
             * <pre>
             * *
             * Map 的 key
             * uid uid
             * sid 顶级频道
             * ssid 子频道
             * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
             * </pre>
             *
             * <code>map&lt;string, string&gt; extend = 1;</code>
             */

            public Builder removeExtend(
                    String key) {
                if (key == null) {
                    throw new NullPointerException();
                }
                getMutableExtend().remove(key);
                return this;
            }

            /**
             * Use alternate mutation accessors instead.
             */
            @Deprecated
            public java.util.Map<String, String>
            getMutableExtend() {
                return internalGetMutableExtend().getMutableMap();
            }

            /**
             * <pre>
             * *
             * Map 的 key
             * uid uid
             * sid 顶级频道
             * ssid 子频道
             * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
             * </pre>
             *
             * <code>map&lt;string, string&gt; extend = 1;</code>
             */
            public Builder putExtend(
                    String key,
                    String value) {
                if (key == null) {
                    throw new NullPointerException();
                }
                if (value == null) {
                    throw new NullPointerException();
                }
                getMutableExtend().put(key, value);
                return this;
            }

            /**
             * <pre>
             * *
             * Map 的 key
             * uid uid
             * sid 顶级频道
             * ssid 子频道
             * terminal 终端类型：1 pc开播，4 移动端，6 showu，7 yy 伴侣，102 rtmp 推流
             * </pre>
             *
             * <code>map&lt;string, string&gt; extend = 1;</code>
             */

            public Builder putAllExtend(
                    java.util.Map<String, String> values) {
                getMutableExtend().putAll(values);
                return this;
            }

            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return this;
            }

            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return this;
            }


            // @@protoc_insertion_point(builder_scope:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap)
        }

        // @@protoc_insertion_point(class_scope:com.yy.revenue.activity.clients.lpfm2YYP.domain.pb.QueryLivingDataMap)
        private static final QueryLivingDataMap DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new QueryLivingDataMap();
        }

        public static QueryLivingDataMap getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<QueryLivingDataMap>
                PARSER = new com.google.protobuf.AbstractParser<QueryLivingDataMap>() {
            public QueryLivingDataMap parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return new QueryLivingDataMap(input, extensionRegistry);
            }
        };

        public static com.google.protobuf.Parser<QueryLivingDataMap> parser() {
            return PARSER;
        }

        @Override
        public com.google.protobuf.Parser<QueryLivingDataMap> getParserForType() {
            return PARSER;
        }

        public QueryLivingDataMap getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_ExtendInfoEntry_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_ExtendInfoEntry_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_ExtendInfoEntry_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_ExtendInfoEntry_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_ExtendEntry_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_ExtendEntry_fieldAccessorTable;

    public static com.google.protobuf.Descriptors.FileDescriptor
    getDescriptor() {
        return descriptor;
    }

    private static com.google.protobuf.Descriptors.FileDescriptor
            descriptor;

    static {
        String[] descriptorData = {
                "\n\016lpfm2YYP.proto\0222com.yy.revenue.activit" +
                        "y.clients.lpfm2YYP.domain.pb\032\turi.proto\"" +
                        "\227\002\n\027GetPublishInfoByUidsReq\022\017\n\007srvname\030\001" +
                        " \001(\t\022\r\n\005appID\030\002 \001(\003\022\016\n\006hostID\030\003 \001(\005\022\023\n\013f" +
                        "ilterRules\030\004 \003(\r\022\014\n\004uids\030\005 \003(\004\022o\n\nextend" +
                        "Info\030\006 \003(\0132[.com.yy.revenue.activity.cli" +
                        "ents.lpfm2YYP.domain.pb.GetPublishInfoBy" +
                        "UidsReq.ExtendInfoEntry\0321\n\017ExtendInfoEnt" +
                        "ry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001:\005\310>\362\231" +
                        "\003\"\261\002\n\027GetPublishInfoByUidsRsp\022\016\n\006result\030",
                "\001 \001(\r\022[\n\013curLiveList\030\002 \003(\0132F.com.yy.reve" +
                        "nue.activity.clients.lpfm2YYP.domain.pb." +
                        "QueryLivingDataMap\022o\n\nextendInfo\030\003 \003(\0132[" +
                        ".com.yy.revenue.activity.clients.lpfm2YY" +
                        "P.domain.pb.GetPublishInfoByUidsRsp.Exte" +
                        "ndInfoEntry\0321\n\017ExtendInfoEntry\022\013\n\003key\030\001 " +
                        "\001(\t\022\r\n\005value\030\002 \001(\t:\0028\001:\005\310>\362\233\003\"\247\001\n\022QueryL" +
                        "ivingDataMap\022b\n\006extend\030\001 \003(\0132R.com.yy.re" +
                        "venue.activity.clients.lpfm2YYP.domain.p" +
                        "b.QueryLivingDataMap.ExtendEntry\032-\n\013Exte",
                "ndEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\0012" +
                        "\323\001\n\036Lpfm2SdkLiveRoomInfoYypService\022\260\001\n\024G" +
                        "etPublishInfoByUids\022K.com.yy.revenue.act" +
                        "ivity.clients.lpfm2YYP.domain.pb.GetPubl" +
                        "ishInfoByUidsRsp\032K.com.yy.revenue.activi" +
                        "ty.clients.lpfm2YYP.domain.pb.GetPublish" +
                        "InfoByUidsRspB\014B\010lpfm2YYPP\000b\006proto3"
        };
        com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
                new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
                    public com.google.protobuf.ExtensionRegistry assignDescriptors(
                            com.google.protobuf.Descriptors.FileDescriptor root) {
                        descriptor = root;
                        return null;
                    }
                };
        com.google.protobuf.Descriptors.FileDescriptor
                .internalBuildGeneratedFileFrom(descriptorData,
                        new com.google.protobuf.Descriptors.FileDescriptor[]{
                                yyp.Uri.getDescriptor(),
                        }, assigner);
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_descriptor =
                getDescriptor().getMessageTypes().get(0);
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_descriptor,
                new String[]{"Srvname", "AppID", "HostID", "FilterRules", "Uids", "ExtendInfo",});
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_ExtendInfoEntry_descriptor =
                internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_descriptor.getNestedTypes().get(0);
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_ExtendInfoEntry_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsReq_ExtendInfoEntry_descriptor,
                new String[]{"Key", "Value",});
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_descriptor =
                getDescriptor().getMessageTypes().get(1);
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_descriptor,
                new String[]{"Result", "CurLiveList", "ExtendInfo",});
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_ExtendInfoEntry_descriptor =
                internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_descriptor.getNestedTypes().get(0);
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_ExtendInfoEntry_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_GetPublishInfoByUidsRsp_ExtendInfoEntry_descriptor,
                new String[]{"Key", "Value",});
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_descriptor =
                getDescriptor().getMessageTypes().get(2);
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_descriptor,
                new String[]{"Extend",});
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_ExtendEntry_descriptor =
                internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_descriptor.getNestedTypes().get(0);
        internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_ExtendEntry_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_com_yy_revenue_activity_clients_lpfm2YYP_domain_pb_QueryLivingDataMap_ExtendEntry_descriptor,
                new String[]{"Key", "Value",});
        com.google.protobuf.ExtensionRegistry registry =
                com.google.protobuf.ExtensionRegistry.newInstance();
        registry.add(yyp.Uri.uri);
        com.google.protobuf.Descriptors.FileDescriptor
                .internalUpdateFileDescriptor(descriptor, registry);
        yyp.Uri.getDescriptor();
    }

    // @@protoc_insertion_point(outer_class_scope)
}
