package com.yy.gameecology.activity.worker.aspect;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.hd.qly.client.ReporterClient;
import com.yy.hd.qly.client.model.ActReportData;
import io.opentelemetry.api.trace.Span;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * SvcSDKService方法调用运维数据上报切面
 * 监控继承了BaseActComponent的类中对SvcSDKService方法的调用，并上报运维数据
 * 
 * <AUTHOR> Generated
 * @date 2025-01-04
 */
@Aspect
@Component
public class SvcSDKServiceReportAspect {
    
    private static final Logger log = LoggerFactory.getLogger(SvcSDKServiceReportAspect.class);
    
    @Autowired(required = false)
    private ReporterClient reporterClient;
    
    /**
     * 定义切点：拦截SvcSDKService接口的所有方法调用
     */
    @Pointcut("execution(* com.yy.gameecology.activity.service.SvcSDKService.*(..))")
    public void svcSDKServiceMethods() {
    }
    
    /**
     * 环绕通知：在SvcSDKService方法调用前后进行处理
     * 只有当调用者是BaseActComponent的子类时才进行数据上报
     */
    @Around("svcSDKServiceMethods()")
    public Object aroundSvcSDKServiceCall(ProceedingJoinPoint joinPoint) throws Throwable {
        // 检查功能开关
        boolean reportEnabled = Const.GEPM.getParamValueToInt(GeParamName.SVC_SDK_REPORT_SWITCH, 1) == 1;
        if (!reportEnabled) {
            return joinPoint.proceed();
        }

        // 历史环境不进行上报
        if (SysEvHelper.checkHistory("SvcSDKService运维数据上报", false)) {
            return joinPoint.proceed();
        }

        Object result = null;
        boolean shouldReport = false;
        String methodName = null;
        String parametersJson = null;
        String callerClassName = null;

        try {
            // 获取方法签名
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            methodName = signature.getName();

            // 获取调用栈，查找是否有BaseActComponent的子类调用
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            for (StackTraceElement element : stackTrace) {
                try {
                    Class<?> callerClass = Class.forName(element.getClassName());
                    if (BaseActComponent.class.isAssignableFrom(callerClass) &&
                        !BaseActComponent.class.equals(callerClass)) {
                        shouldReport = true;
                        callerClassName = callerClass.getSimpleName();
                        break;
                    }
                } catch (ClassNotFoundException e) {
                    // 忽略找不到的类
                }
            }

            // 如果需要上报，序列化参数
            if (shouldReport) {
                Object[] args = joinPoint.getArgs();
                try {
                    parametersJson = JSON.toJSONString(args);
                } catch (Exception e) {
                    parametersJson = "参数序列化失败: " + e.getMessage();
                    log.warn("参数序列化失败，方法: {}, 错误: {}", methodName, e.getMessage());
                }
            }

            // 执行原方法
            result = joinPoint.proceed();

            // 异步上报数据
            if (shouldReport && reporterClient != null) {
                asyncReportData(methodName, parametersJson, callerClassName);
            }

        } catch (Throwable throwable) {
            // 如果需要上报且出现异常，也进行上报
            if (shouldReport && reporterClient != null) {
                asyncReportData(methodName, parametersJson + " [异常: " + throwable.getMessage() + "]", callerClassName);
            }
            throw throwable;
        }

        return result;
    }
    
    /**
     * 异步上报运维数据
     * 
     * @param methodName 调用的方法名
     * @param content 参数内容（JSON序列化后的字符串）
     * @param callerClassName 调用者类名
     */
    private void asyncReportData(String methodName, String content, String callerClassName) {
        try {
            // 创建上报数据
            ActReportData reportData = ReporterClient.createManualReport(
                "hdpt",                    // 系统标识
                "svc_sdk_monitor",         // 业务标识
                "SvcSDKService方法调用监控", // 业务描述
                methodName,                // method字段：调用的方法名
                content                    // content字段：参数的JSON序列化
            );
            
            // 设置追踪ID
            String traceId = Span.current().getSpanContext().getTraceId();
            if (traceId != null && !traceId.isEmpty()) {
                reportData.setTraceId(traceId);
            }
            
            // 设置调用者信息作为扩展信息
            // 注意：ActReportData可能没有setExtInfo方法，这里先注释掉
            // reportData.setExtInfo("caller=" + callerClassName);
            
            // 异步发送
            CompletableFuture<Boolean> future = reporterClient.sendAsync(reportData);
            
            // 添加回调处理结果（可选）
            future.whenComplete((success, throwable) -> {
                if (throwable != null) {
                    log.warn("运维数据上报失败，方法: {}, 调用者: {}, 错误: {}", 
                            methodName, callerClassName, throwable.getMessage());
                } else if (Boolean.TRUE.equals(success)) {
                    log.debug("运维数据上报成功，方法: {}, 调用者: {}", methodName, callerClassName);
                } else {
                    log.warn("运维数据上报返回失败，方法: {}, 调用者: {}", methodName, callerClassName);
                }
            });
            
        } catch (Exception e) {
            log.error("创建运维数据上报时发生异常，方法: {}, 调用者: {}, 错误: {}", 
                     methodName, callerClassName, e.getMessage(), e);
        }
    }
}
