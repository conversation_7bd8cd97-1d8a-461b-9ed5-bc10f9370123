# 运维数据上报模块

## 功能概述

本模块提供了完整的运维数据上报功能，包括：
1. **ReportContext**: 线程变量管理，用于存储和获取上下文信息
2. **SvcSDKServiceReportAspect**: AOP切面，自动拦截SvcSDKService调用并上报数据

## 核心组件

### 1. ReportContext - 线程变量管理类

`ReportContext`提供了两套独立的线程变量管理功能：

#### 组件上下文管理
- **actId**: 活动ID
- **cmptId**: 组件ID  
- **cmptIndex**: 组件索引

#### 榜单上下文管理
- **actId**: 活动ID
- **rankId**: 榜单ID
- **phaseId**: 阶段ID
- **timeCode**: 时间编码

### 2. SvcSDKServiceReportAspect - 运维数据上报切面

自动拦截继承了`BaseActComponent`的类中对`SvcSDKService`方法的调用，并上报运维数据。

## 使用方法

### 组件上下文使用

```java
// 设置组件上下文信息
ReportContext.setComponentContext(actId, cmptId, cmptIndex);

// 获取组件上下文信息
Long actId = ReportContext.getComponentActId();
Long cmptId = ReportContext.getComponentId();
Long cmptIndex = ReportContext.getComponentIndex();

// 获取完整的组件上下文对象
ReportContext.ComponentContext context = ReportContext.getComponentContext();

// 清除组件上下文
ReportContext.clearComponentContext();
```

### 榜单上下文使用

```java
// 设置榜单上下文信息
ReportContext.setRankingContext(actId, rankId, phaseId, timeCode);

// 获取榜单上下文信息
Long actId = ReportContext.getRankingActId();
Long rankId = ReportContext.getRankId();
Long phaseId = ReportContext.getPhaseId();
String timeCode = ReportContext.getTimeCode();

// 获取完整的榜单上下文对象
ReportContext.RankingContext context = ReportContext.getRankingContext();

// 清除榜单上下文
ReportContext.clearRankingContext();
```

### 便利方法

```java
// 清除所有上下文信息
ReportContext.clearAllContext();

// 移除所有ThreadLocal变量（线程结束时调用）
ReportContext.removeAllContext();

// 获取优先的活动ID（组件上下文优先）
Long preferredActId = ReportContext.getPreferredActId();

// 检查是否有任何上下文信息
boolean hasContext = ReportContext.hasAnyContext();

// 获取所有上下文信息的字符串表示
String allInfo = ReportContext.getAllContextInfo();
```

## 实际使用示例

### 示例1：组件方法中设置上下文

```java
@Component
public class MyComponent extends BaseActComponent<MyComponentAttr> {
    
    @Autowired
    private SvcSDKService svcSDKService;
    
    public void processData(Long actId, Long cmptIndex) {
        try {
            // 设置组件上下文
            ReportContext.setComponentContext(actId, getComponentId(), cmptIndex);
            
            // 调用SvcSDKService方法
            // 切面会自动从ReportContext获取上下文信息并上报
            svcSDKService.unicastUid(uid, message);
            
        } finally {
            // 清理上下文
            ReportContext.clearComponentContext();
        }
    }
}
```

### 示例2：榜单操作中设置上下文

```java
public void updateRanking(Long actId, Long rankId, Long phaseId, String timeCode) {
    try {
        // 设置榜单上下文
        ReportContext.setRankingContext(actId, rankId, phaseId, timeCode);
        
        // 执行榜单相关操作
        performRankingOperation();
        
    } finally {
        // 清理上下文
        ReportContext.clearRankingContext();
    }
}
```

### 示例3：混合上下文使用

```java
public void complexOperation() {
    try {
        // 设置组件上下文
        ReportContext.setComponentContext(actId, cmptId, cmptIndex);
        
        // 设置榜单上下文
        ReportContext.setRankingContext(actId, rankId, phaseId, timeCode);
        
        // 执行复杂操作
        performComplexOperation();
        
    } finally {
        // 清理所有上下文
        ReportContext.clearAllContext();
    }
}
```

## 上报数据格式

当切面拦截到SvcSDKService方法调用时，会自动从ReportContext获取上下文信息并上报：

- **method**: SvcSDKService的方法名
- **content**: 参数的智能JSON序列化（PB对象只包含有值字段）
- **actId**: 从ReportContext获取的活动ID
- **extInt**: 组件ID (cmptId)
- **extLong**: 组件索引 (cmptIndex)
- **extString**: 调用者和组件信息

## 线程安全性

- **ThreadLocal隔离**: 每个线程都有独立的上下文信息，互不影响
- **自动初始化**: ThreadLocal变量会自动初始化为空的上下文对象
- **内存管理**: 提供了清理和移除方法，避免内存泄漏

## 最佳实践

### 1. 及时清理上下文

```java
try {
    ReportContext.setComponentContext(actId, cmptId, cmptIndex);
    // 业务逻辑
} finally {
    ReportContext.clearComponentContext(); // 及时清理
}
```

### 2. 线程结束时移除ThreadLocal

```java
// 在线程池或长期运行的线程中
try {
    // 业务逻辑
} finally {
    ReportContext.removeAllContext(); // 完全移除ThreadLocal
}
```

### 3. 异常处理

```java
try {
    ReportContext.setComponentContext(actId, cmptId, cmptIndex);
    // 可能抛出异常的业务逻辑
} catch (Exception e) {
    // 异常处理
} finally {
    ReportContext.clearComponentContext(); // 确保清理
}
```

### 4. 嵌套方法调用

```java
public void outerMethod() {
    ReportContext.setComponentContext(actId, cmptId, cmptIndex);
    try {
        innerMethod(); // 内层方法可以直接使用上下文
    } finally {
        ReportContext.clearComponentContext();
    }
}

private void innerMethod() {
    // 直接获取外层设置的上下文
    Long actId = ReportContext.getComponentActId();
    // 使用上下文信息
}
```

## 测试

### 单元测试
- `ReportContextTest`: 测试ReportContext的基本功能
- `ReportContextUsageExample`: 展示实际使用场景

### 运行测试
```bash
mvn test -Dtest=ReportContextTest
```

## 注意事项

1. **内存泄漏**: 在长期运行的线程中，务必调用`removeAllContext()`
2. **线程隔离**: 不同线程的上下文信息是独立的
3. **null值处理**: 所有方法都正确处理null值
4. **性能影响**: ThreadLocal操作性能开销很小，可以放心使用
5. **上下文传递**: 上下文信息不会自动传递到新创建的线程中
