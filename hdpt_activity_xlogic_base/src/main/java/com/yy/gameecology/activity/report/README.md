# 运维数据上报模块

## 功能概述

本模块提供了完整的运维数据上报功能，包括：
1. **ReportContext**: 基于MDC的上下文管理，支持多线程参数传递
2. **SvcSDKServiceReportAspect**: AOP切面，自动拦截SvcSDKService调用并上报数据

## 核心组件

### 1. ReportContext - 基于MDC的上下文管理类

`ReportContext`使用SLF4J的MDC（Mapped Diagnostic Context）来管理上下文信息，支持多线程参数传递。提供了两套独立的上下文管理功能：

#### 组件上下文管理
- **actId**: 活动ID
- **cmptId**: 组件ID  
- **cmptIndex**: 组件索引

#### 榜单上下文管理
- **actId**: 活动ID
- **rankId**: 榜单ID
- **phaseId**: 阶段ID
- **timeCode**: 时间编码

### 2. ReportContextResolver - 参数解析器

`ReportContextResolver`用于从继承自`BaseEvent`的类中提取参数，并自动设置到`ReportContext`中。

#### 核心功能
- **智能参数提取**: 从BaseEvent子类中提取actId、rankId、phaseId、timeCode
- **字段检测**: 自动检测子类是否包含特定字段
- **时间编码计算**: 根据timeKey和endTime计算timeCode
- **参数验证**: 验证提取的参数是否有效
- **批量处理**: 支持批量处理多个事件

#### 支持的字段
- **actId**: 活动ID（必需）
- **rankId**: 榜单ID（必需）
- **phaseId**: 阶段ID（可选，如果子类没有则返回0）
- **timeCode**: 时间编码（可选，从endTime和timeKey计算得出）

### 3. SvcSDKServiceReportAspect - 运维数据上报切面

自动拦截继承了`BaseActComponent`的类中对`SvcSDKService`方法的调用，并上报运维数据。

## 使用方法

### 组件上下文使用

```java
// 设置组件上下文信息
ReportContext.setComponentContext(actId, cmptId, cmptIndex);

// 获取组件上下文信息
Long actId = ReportContext.getComponentActId();
Long cmptId = ReportContext.getComponentId();
Long cmptIndex = ReportContext.getComponentIndex();

// 获取完整的组件上下文对象
ReportContext.ComponentContext context = ReportContext.getComponentContext();

// 清除组件上下文
ReportContext.clearComponentContext();
```

### 榜单上下文使用

```java
// 设置榜单上下文信息
ReportContext.setRankingContext(actId, rankId, phaseId, timeCode);

// 获取榜单上下文信息
Long actId = ReportContext.getRankingActId();
Long rankId = ReportContext.getRankId();
Long phaseId = ReportContext.getPhaseId();
String timeCode = ReportContext.getTimeCode();

// 获取完整的榜单上下文对象
ReportContext.RankingContext context = ReportContext.getRankingContext();

// 清除榜单上下文
ReportContext.clearRankingContext();
```

### 便利方法

```java
// 清除所有上下文信息
ReportContext.clearAllContext();

// 移除所有ThreadLocal变量（线程结束时调用）
ReportContext.removeAllContext();

// 获取优先的活动ID（组件上下文优先）
Long preferredActId = ReportContext.getPreferredActId();

// 检查是否有任何上下文信息
boolean hasContext = ReportContext.hasAnyContext();

// 获取所有上下文信息的字符串表示
String allInfo = ReportContext.getAllContextInfo();
```

### ReportContextResolver使用

```java
// 从BaseEvent子类中提取参数并设置榜单上下文
PhaseTimeEnd event = new PhaseTimeEnd();
event.setActId(2025010401L);
event.setRankId(1001L);
event.setPhaseId(2001L);
event.setTimeKey(1L);
event.setEndTime("2025-01-04 23:59:59");

// 一键解析并设置上下文
boolean success = ReportContextResolver.resolveAndSetRankingContext(event);

// 单独提取参数
Long actId = ReportContextResolver.extractActId(event);
Long rankId = ReportContextResolver.extractRankId(event);
Long phaseId = ReportContextResolver.extractPhaseId(event);
String timeCode = ReportContextResolver.extractTimeCode(event);

// 字段检测
boolean hasPhaseId = ReportContextResolver.hasField(event, "phaseId");
boolean hasTimeKey = ReportContextResolver.hasField(event, "timeKey");

// 参数验证
ReportContextResolver.ValidationResult result = ReportContextResolver.validateEvent(event);
if (result.isValid()) {
    // 处理有效事件
}

// 获取字段摘要
String summary = ReportContextResolver.getFieldSummary(event);

// 批量处理
int successCount = ReportContextResolver.batchResolveAndSetRankingContext(event1, event2, event3);
```

## 实际使用示例

### 示例1：组件方法中设置上下文

```java
@Component
public class MyComponent extends BaseActComponent<MyComponentAttr> {
    
    @Autowired
    private SvcSDKService svcSDKService;
    
    public void processData(Long actId, Long cmptIndex) {
        try {
            // 设置组件上下文
            ReportContext.setComponentContext(actId, getComponentId(), cmptIndex);
            
            // 调用SvcSDKService方法
            // 切面会自动从ReportContext获取上下文信息并上报
            svcSDKService.unicastUid(uid, message);
            
        } finally {
            // 清理上下文
            ReportContext.clearComponentContext();
        }
    }
}
```

### 示例2：榜单操作中设置上下文

```java
public void updateRanking(Long actId, Long rankId, Long phaseId, String timeCode) {
    try {
        // 设置榜单上下文
        ReportContext.setRankingContext(actId, rankId, phaseId, timeCode);
        
        // 执行榜单相关操作
        performRankingOperation();
        
    } finally {
        // 清理上下文
        ReportContext.clearRankingContext();
    }
}
```

### 示例3：混合上下文使用

```java
public void complexOperation() {
    try {
        // 设置组件上下文
        ReportContext.setComponentContext(actId, cmptId, cmptIndex);
        
        // 设置榜单上下文
        ReportContext.setRankingContext(actId, rankId, phaseId, timeCode);
        
        // 执行复杂操作
        performComplexOperation();
        
    } finally {
        // 清理所有上下文
        ReportContext.clearAllContext();
    }
}
```

### 示例4：事件处理器中使用ReportContextResolver

```java
@Component
public class EventHandler {

    @EventListener
    public void handlePhaseTimeEnd(PhaseTimeEnd event) {
        try {
            // 验证事件
            ReportContextResolver.ValidationResult validation =
                ReportContextResolver.validateEvent(event);

            if (!validation.isValid()) {
                log.warn("事件验证失败: {}", validation.getMessage());
                return;
            }

            // 解析并设置榜单上下文
            boolean success = ReportContextResolver.resolveAndSetRankingContext(event);
            if (success) {
                log.info("榜单上下文设置成功: {}", ReportContext.getRankingContext());

                // 执行业务逻辑，可以通过ReportContext获取上下文信息
                processPhaseEnd();

                // 如果业务逻辑中调用了SvcSDKService，切面会自动上报数据

            } else {
                log.error("设置榜单上下文失败");
            }

        } finally {
            // 清理上下文
            ReportContext.clearRankingContext();
        }
    }

    private void processPhaseEnd() {
        // 业务逻辑可以通过ReportContext获取上下文信息
        Long actId = ReportContext.getRankingActId();
        Long rankId = ReportContext.getRankId();
        Long phaseId = ReportContext.getPhaseId();
        String timeCode = ReportContext.getTimeCode();

        // 执行具体的业务逻辑
    }
}
```

## 上报数据格式

当切面拦截到SvcSDKService方法调用时，会自动从ReportContext获取上下文信息并上报：

- **method**: SvcSDKService的方法名
- **content**: 参数的智能JSON序列化（PB对象只包含有值字段）
- **actId**: 从ReportContext获取的活动ID
- **extInt**: 组件ID (cmptId)
- **extLong**: 组件索引 (cmptIndex)
- **extString**: 调用者和组件信息

## MDC优势

- **多线程传递**: 支持项目中统一的MDC上下文传递机制
- **线程隔离**: 每个线程都有独立的MDC上下文，互不影响
- **自动继承**: 子线程可以继承父线程的MDC上下文（需要显式传递）
- **内存安全**: MDC会在线程结束时自动清理

## MDC Key定义

### 组件上下文
- `report.component.actId`: 活动ID
- `report.component.cmptId`: 组件ID
- `report.component.cmptIndex`: 组件索引

### 榜单上下文
- `report.ranking.actId`: 活动ID
- `report.ranking.rankId`: 榜单ID
- `report.ranking.phaseId`: 阶段ID
- `report.ranking.timeCode`: 时间编码

## 最佳实践

### 1. 及时清理上下文

```java
try {
    ReportContext.setComponentContext(actId, cmptId, cmptIndex);
    // 业务逻辑
} finally {
    ReportContext.clearComponentContext(); // 及时清理
}
```

### 2. 多线程MDC传递

```java
// 父线程设置上下文
ReportContext.setComponentContext(actId, cmptId, cmptIndex);

// 获取父线程的MDC
Map<String, String> parentMDC = MDC.getCopyOfContextMap();

// 在子线程中继承父线程的MDC
CompletableFuture.runAsync(() -> {
    try {
        // 设置子线程的MDC
        if (parentMDC != null) {
            MDC.setContextMap(parentMDC);
        }

        // 子线程可以使用父线程的上下文
        Long actId = ReportContext.getComponentActId();

        // 业务逻辑

    } finally {
        // 清理子线程的MDC
        MDC.clear();
    }
});
```

### 3. 异常处理

```java
try {
    ReportContext.setComponentContext(actId, cmptId, cmptIndex);
    // 可能抛出异常的业务逻辑
} catch (Exception e) {
    // 异常处理
} finally {
    ReportContext.clearComponentContext(); // 确保清理
}
```

### 4. 嵌套方法调用

```java
public void outerMethod() {
    ReportContext.setComponentContext(actId, cmptId, cmptIndex);
    try {
        innerMethod(); // 内层方法可以直接使用上下文
    } finally {
        ReportContext.clearComponentContext();
    }
}

private void innerMethod() {
    // 直接获取外层设置的上下文
    Long actId = ReportContext.getComponentActId();
    // 使用上下文信息
}
```

## 测试

### 单元测试
- `ReportContextTest`: 测试ReportContext的基本功能
- `ReportContextUsageExample`: 展示实际使用场景

### 运行测试
```bash
mvn test -Dtest=ReportContextTest
```

## 注意事项

1. **MDC传递**: 项目中已有统一的MDC传递机制，新线程会自动继承父线程的MDC上下文
2. **线程隔离**: 不同线程的MDC上下文是独立的，修改不会相互影响
3. **null值处理**: 所有方法都正确处理null值，不会向MDC中放入null
4. **性能影响**: MDC操作性能开销很小，可以放心使用
5. **自动清理**: MDC会在线程结束时自动清理，无需手动管理内存
6. **Key命名**: 使用统一的前缀`report.`来避免与其他MDC Key冲突
