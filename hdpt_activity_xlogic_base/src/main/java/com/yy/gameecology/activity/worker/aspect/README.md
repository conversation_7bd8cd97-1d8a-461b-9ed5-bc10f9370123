# SvcSDKService运维数据上报切面

## 功能描述

本切面用于监控继承了`BaseActComponent`的类中对`SvcSDKService`方法的调用，并自动上报运维数据到运维系统。

## 实现原理

使用Spring AOP技术，通过`@Around`注解拦截`SvcSDKService`接口的所有方法调用。当检测到调用者是`BaseActComponent`的子类时，会自动收集调用信息并异步上报到运维系统。

## 核心组件

### 1. SvcSDKServiceReportAspect
- **位置**: `com.yy.gameecology.activity.worker.aspect.SvcSDKServiceReportAspect`
- **功能**: 核心切面类，负责拦截方法调用和数据上报
- **切点**: `execution(* com.yy.gameecology.activity.service.SvcSDKService.*(..))`

### 2. AopConfig
- **位置**: `com.yy.gameecology.activity.config.AopConfig`
- **功能**: AOP配置类，启用AspectJ自动代理

## 上报数据格式

上报的数据包含以下信息：

- **系统标识**: "hdpt"
- **业务标识**: "svc_sdk_monitor"
- **业务描述**: "SvcSDKService方法调用监控"
- **method字段**: 调用的SvcSDKService方法名
- **content字段**: 传入参数的智能JSON序列化字符串
- **actId**: 活动ID（从线程上下文获取）
- **extInt**: 组件ID（cmptId）
- **extLong**: 组件索引（cmptIndex）
- **extString**: 调用者信息和组件详情
- **traceId**: 当前请求的追踪ID（如果存在）

## 智能参数序列化

切面采用智能序列化策略来处理不同类型的参数：

### Protocol Buffer对象处理
- **只序列化有值的字段**: 使用`JsonFormat.printer()`只输出PB对象中实际设置了值的字段
- **包含类型信息**: 添加`_messageType`字段标识PB消息类型
- **错误处理**: 如果PB序列化失败，会回退到基本信息和toString()结果

### 普通对象处理
- **基本类型直接返回**: String、Number、Boolean等基本类型直接序列化
- **大对象摘要**: 如果序列化结果超过1000字符，只保留前200字符作为摘要
- **类型信息**: 包含对象的类型信息便于调试

### 序列化示例

```json
[
  1001,
  {
    "_messageType": "GameEcologyMsg",
    "uri": 12345,
    "data": "test data",
    "seq": 98765
  }
]
```

## 触发条件

只有当以下条件同时满足时，才会触发运维数据上报：

1. 调用了`SvcSDKService`接口的任何方法
2. 调用者是`BaseActComponent`的子类
3. `ReporterClient`已正确注入
4. 功能开关`svc_sdk_report_switch`为开启状态（默认开启）
5. 非历史环境

## 配置要求

### 1. Maven依赖

确保项目中包含以下依赖：

```xml
<!-- Spring Boot AOP Starter -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-aop</artifactId>
</dependency>

<!-- QlyClient for reporting -->
<dependency>
    <groupId>com.yy.hd.qly</groupId>
    <artifactId>qly-client</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</dependency>

<!-- Protocol Buffers -->
<dependency>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-java-util</artifactId>
</dependency>
```

### 2. Spring配置

确保Spring Boot应用启用了AOP功能（通过`AopConfig`类自动配置）。

### 3. ReporterClient配置

确保`ReporterClient`在Spring容器中正确配置和注入。

### 4. 功能开关

可以通过`ge_parameter`表中的`svc_sdk_report_switch`参数控制功能开关：
- 1: 开启上报（默认）
- 0: 关闭上报

## 使用示例

### 正常触发场景

```java
@Component
public class MyActComponent extends BaseActComponent<MyComponentAttr> {
    
    @Autowired
    private SvcSDKService svcSDKService;
    
    public void doSomething() {
        // 创建PB消息，只设置需要的字段
        GameEcologyMsg.Builder builder = GameEcologyMsg.newBuilder();
        builder.setUri(12345);
        builder.setData("test data");
        GameEcologyMsg message = builder.build();
        
        // 这个调用会触发运维数据上报，只序列化有值的字段
        svcSDKService.unicastUid(uid, message);
    }
}
```

### 不会触发的场景

```java
@Service
public class MyService {  // 不继承BaseActComponent
    
    @Autowired
    private SvcSDKService svcSDKService;
    
    public void doSomething() {
        // 这个调用不会触发运维数据上报
        svcSDKService.unicastUid(uid, message);
    }
}
```

## 测试

可以运行测试类来验证功能：

```bash
mvn test -Dtest=SvcSDKServiceReportAspectTest
```

测试类位置：`com.yy.gameecology.activity.worker.aspect.SvcSDKServiceReportAspectTest`

## 日志监控

切面会输出以下日志：

- **DEBUG级别**: 成功上报的信息
- **WARN级别**: 上报失败或参数序列化失败的警告
- **ERROR级别**: 创建上报数据时的异常

## 性能考虑

1. **异步上报**: 数据上报采用异步方式，不会阻塞业务逻辑
2. **智能序列化**: 只序列化PB对象中有值的字段，减少数据量
3. **大对象处理**: 对于过大的对象，只保留摘要信息
4. **异常隔离**: 上报过程中的异常不会影响原始方法的执行
5. **条件检查**: 只有满足条件的调用才会进行上报，减少不必要的开销

## 注意事项

1. 确保`ReporterClient`配置正确，否则上报功能不会生效
2. PB对象序列化采用JsonFormat，只包含有值的字段
3. 大对象会被截断为摘要，避免序列化结果过大
4. 切面只监控`SvcSDKService`接口的方法，不包括其实现类的私有方法
5. 调用栈分析可能在某些复杂场景下不够准确

## 故障排查

### 1. 切面不生效

- 检查AOP配置是否正确
- 确认Spring Boot版本支持AOP
- 查看是否有其他AOP配置冲突

### 2. 数据不上报

- 检查`ReporterClient`是否正确注入
- 确认调用者是否继承了`BaseActComponent`
- 检查功能开关`svc_sdk_report_switch`是否开启
- 查看日志中的错误信息

### 3. 参数序列化失败

- 检查PB对象是否正确构建
- 查看具体的序列化错误日志
- 确认protobuf-java-util依赖是否正确引入

### 4. 序列化结果过大

- 系统会自动处理大对象，只保留摘要
- 可以通过日志查看实际的序列化结果大小
