# SvcSDKService运维数据上报切面

## 功能描述

本切面用于监控继承了`BaseActComponent`的类中对`SvcSDKService`方法的调用，并自动上报运维数据到运维系统。

## 实现原理

使用Spring AOP技术，通过`@Around`注解拦截`SvcSDKService`接口的所有方法调用。当检测到调用者是`BaseActComponent`的子类时，会自动收集调用信息并异步上报到运维系统。

## 核心组件

### 1. SvcSDKServiceReportAspect
- **位置**: `com.yy.gameecology.activity.worker.aspect.SvcSDKServiceReportAspect`
- **功能**: 核心切面类，负责拦截方法调用和数据上报
- **切点**: `execution(* com.yy.gameecology.activity.service.SvcSDKService.*(..))`

### 2. AopConfig
- **位置**: `com.yy.gameecology.activity.config.AopConfig`
- **功能**: AOP配置类，启用AspectJ自动代理

## 上报数据格式

上报的数据包含以下信息：

- **系统标识**: "hdpt"
- **业务标识**: "svc_sdk_monitor"
- **业务描述**: "SvcSDKService方法调用监控"
- **method字段**: 调用的SvcSDKService方法名
- **content字段**: 传入参数的JSON序列化字符串
- **traceId**: 当前请求的追踪ID（如果存在）

## 触发条件

只有当以下条件同时满足时，才会触发运维数据上报：

1. 调用了`SvcSDKService`接口的任何方法
2. 调用者是`BaseActComponent`的子类
3. `ReporterClient`已正确注入

## 配置要求

### 1. Maven依赖

确保项目中包含以下依赖：

```xml
<!-- Spring Boot AOP Starter -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-aop</artifactId>
</dependency>

<!-- QlyClient for reporting -->
<dependency>
    <groupId>com.yy.hd.qly</groupId>
    <artifactId>qly-client</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</dependency>
```

### 2. Spring配置

确保Spring Boot应用启用了AOP功能（通过`AopConfig`类自动配置）。

### 3. ReporterClient配置

确保`ReporterClient`在Spring容器中正确配置和注入。

## 使用示例

### 正常触发场景

```java
@Component
public class MyActComponent extends BaseActComponent<MyComponentAttr> {
    
    @Autowired
    private SvcSDKService svcSDKService;
    
    public void doSomething() {
        // 这个调用会触发运维数据上报
        svcSDKService.unicastUid(uid, message);
    }
}
```

### 不会触发的场景

```java
@Service
public class MyService {  // 不继承BaseActComponent
    
    @Autowired
    private SvcSDKService svcSDKService;
    
    public void doSomething() {
        // 这个调用不会触发运维数据上报
        svcSDKService.unicastUid(uid, message);
    }
}
```

## 测试

可以运行测试类来验证功能：

```bash
mvn test -Dtest=SvcSDKServiceReportAspectTest
```

测试类位置：`com.yy.gameecology.activity.worker.aspect.SvcSDKServiceReportAspectTest`

## 日志监控

切面会输出以下日志：

- **DEBUG级别**: 成功上报的信息
- **WARN级别**: 上报失败或参数序列化失败的警告
- **ERROR级别**: 创建上报数据时的异常

## 性能考虑

1. **异步上报**: 数据上报采用异步方式，不会阻塞业务逻辑
2. **异常隔离**: 上报过程中的异常不会影响原始方法的执行
3. **条件检查**: 只有满足条件的调用才会进行上报，减少不必要的开销

## 注意事项

1. 确保`ReporterClient`配置正确，否则上报功能不会生效
2. 参数序列化可能失败，切面会记录警告日志但不会影响业务
3. 切面只监控`SvcSDKService`接口的方法，不包括其实现类的私有方法
4. 调用栈分析可能在某些复杂场景下不够准确

## 故障排查

### 1. 切面不生效

- 检查AOP配置是否正确
- 确认Spring Boot版本支持AOP
- 查看是否有其他AOP配置冲突

### 2. 数据不上报

- 检查`ReporterClient`是否正确注入
- 确认调用者是否继承了`BaseActComponent`
- 查看日志中的错误信息

### 3. 参数序列化失败

- 检查方法参数是否包含不可序列化的对象
- 查看具体的序列化错误日志
