package com.yy.gameecology.activity.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * AOP配置类
 * 启用AspectJ自动代理功能
 * 
 * <AUTHOR> Generated
 * @date 2025-01-04
 */
@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class AopConfig {
    // 配置类，用于启用AOP功能
    // proxyTargetClass = true 表示使用CGLIB代理而不是JDK动态代理
    // 这样可以代理没有实现接口的类
}
