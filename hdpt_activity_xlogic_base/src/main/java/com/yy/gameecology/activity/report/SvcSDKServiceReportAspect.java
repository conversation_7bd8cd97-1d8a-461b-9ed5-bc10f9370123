package com.yy.gameecology.activity.report;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.hd.qly.client.ReporterClient;
import com.yy.hd.qly.client.model.ActReportData;
import io.opentelemetry.api.trace.Span;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 运维数据上报切面
 * 监控继承了BaseActComponent的类中对以下服务方法的调用，并上报运维数据：
 * 1. SvcSDKService - SDK服务
 * 2. CommonBroadCastService - 通用广播服务
 * 3. KafkaService - Kafka消息服务
 * 4. HdztAwardServiceClient - 奖励服务客户端
 *
 * <AUTHOR> Generated
 * @date 2025-01-04
 */
@Aspect
@Component
public class SvcSDKServiceReportAspect {

    private static final Logger log = LoggerFactory.getLogger(SvcSDKServiceReportAspect.class);

    @Autowired(required = false)
    private ReporterClient reporterClient;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 定义切点：拦截SvcSDKService接口的所有方法调用
     */
    @Pointcut("execution(* com.yy.gameecology.activity.service.SvcSDKService.*(..))")
    public void svcSDKServiceMethods() {
    }

    /**
     * 定义切点：拦截CommonBroadCastService类的所有方法
     */
    @Pointcut("execution(* com.yy.gameecology.activity.service.CommonBroadCastService.*(..))")
    public void commonBroadCastServiceMethods() {
    }

    /**
     * 定义切点：拦截KafkaService类的所有方法
     */
    @Pointcut("execution(* com.yy.gameecology.activity.service.KafkaService.*(..))")
    public void kafkaServiceMethods() {
    }

    /**
     * 定义切点：拦截HdztAwardServiceClient类的所有方法
     */
    @Pointcut("execution(* com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient.*(..))")
    public void hdztAwardServiceClientMethods() {
    }

    /**
     * 组合切点：拦截所有目标服务的方法
     */
    @Pointcut("svcSDKServiceMethods() || commonBroadCastServiceMethods() || kafkaServiceMethods() || hdztAwardServiceClientMethods()")
    public void allTargetServiceMethods() {
    }
    
    /**
     * 环绕通知：在SvcSDKService方法调用前后进行处理
     * 只有当调用者是BaseActComponent的子类时才进行数据上报
     */
    @Around("allTargetServiceMethods()")
    public Object aroundTargetServiceCall(ProceedingJoinPoint joinPoint) throws Throwable {
        // 检查功能开关
        boolean reportEnabled = Const.GEPM.getParamValueToInt(GeParamName.SVC_SDK_REPORT_SWITCH, 1) == 1;
        if (!reportEnabled) {
            return joinPoint.proceed();
        }

        // 历史环境不进行上报
        if (SysEvHelper.checkHistory("运维数据上报", false)) {
            return joinPoint.proceed();
        }

        Object result = null;
        boolean shouldReport = false;
        String methodName = null;
        String parametersJson = null;
        String callerClassName = null;
        String serviceType = null;

        try {
            // 获取方法签名
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            methodName = signature.getName();

            // 识别服务类型
            serviceType = identifyServiceType(joinPoint);

            // 获取调用栈，查找是否有BaseActComponent的子类调用
            ComponentContextInfo contextInfo = extractComponentContextFromStack();
            if (contextInfo != null) {
                shouldReport = true;
                callerClassName = contextInfo.getCallerClassName();
            }

            // 如果需要上报，序列化参数
            if (shouldReport) {
                Object[] args = joinPoint.getArgs();
                try {
                    parametersJson = serializeParameters(args);
                } catch (Exception e) {
                    parametersJson = "参数序列化失败: " + e.getMessage();
                    log.warn("参数序列化失败，方法: {}, 错误: {}", methodName, e.getMessage());
                }
            }

            // 执行原方法
            result = joinPoint.proceed();

            // 异步上报数据
            if (shouldReport && reporterClient != null) {
                asyncReportData(serviceType, methodName, parametersJson, contextInfo);
            }

        } catch (Throwable throwable) {
            // 如果需要上报且出现异常，也进行上报
            if (shouldReport && reporterClient != null) {
                asyncReportData(serviceType, methodName, parametersJson + " [异常: " + throwable.getMessage() + "]",null);
            }
            throw throwable;
        }

        return result;
    }

    /**
     * 组件上下文信息类
     */
    private static class ComponentContextInfo {
        private String callerClassName;
        private Long actId;
        private Long cmptId;
        private Integer cmptIndex;

        public ComponentContextInfo(String callerClassName, Long actId, Long cmptId, Integer cmptIndex) {
            this.callerClassName = callerClassName;
            this.actId = actId;
            this.cmptId = cmptId;
            this.cmptIndex = cmptIndex;
        }

        public String getCallerClassName() { return callerClassName; }
        public Long getActId() { return actId; }
        public Long getCmptId() { return cmptId; }
        public Integer getCmptIndex() { return cmptIndex; }
    }

    /**
     * 从调用栈中提取组件上下文信息
     *
     * @return 组件上下文信息，如果没有找到BaseActComponent调用者则返回null
     */
    private ComponentContextInfo extractComponentContextFromStack() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();

        for (StackTraceElement element : stackTrace) {
            try {
                Class<?> callerClass = Class.forName(element.getClassName());
                if (BaseActComponent.class.isAssignableFrom(callerClass) &&
                    !BaseActComponent.class.equals(callerClass)) {

                    // 尝试从Spring容器中获取该组件的实例
                    try {
                        Object componentBean = applicationContext.getBean(callerClass);
                        if (componentBean instanceof BaseActComponent) {
                            BaseActComponent<?> component = (BaseActComponent<?>) componentBean;
                            Long cmptId = component.getComponentId();

                            // 从ReportContext线程变量获取actId和cmptIndex
                            ReportContext.ComponentContext componentContext = ReportContext.getComponentContext();
                            Long actId = componentContext.getActId();
                            Long cmptIndex = componentContext.getCmptIndex();

                            return new ComponentContextInfo(
                                callerClass.getSimpleName(),
                                actId,     // 从线程变量获取actId
                                cmptId,    // 从组件实例获取cmptId
                                cmptIndex != null ? cmptIndex.intValue() : null  // 从线程变量获取cmptIndex
                            );
                        }
                    } catch (Exception e) {
                        log.debug("无法从Spring容器获取组件实例: {}", callerClass.getName());
                    }

                    // 如果无法获取Spring Bean，至少返回类名信息
                    return new ComponentContextInfo(callerClass.getSimpleName(), null, null, null);
                }
            } catch (ClassNotFoundException e) {
                // 忽略找不到的类
            }
        }

        return null;
    }

    /**
     * 识别服务类型
     *
     * @param joinPoint 连接点
     * @return 服务类型标识
     */
    private String identifyServiceType(ProceedingJoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getSimpleName();

        if (className.contains("SvcSDKService")) {
            return "SvcSDKService";
        } else if (className.contains("CommonBroadCastService")) {
            return "CommonBroadCastService";
        } else if (className.contains("KafkaService")) {
            return "KafkaService";
        } else if (className.contains("HdztAwardServiceClient")) {
            return "HdztAwardServiceClient";
        } else {
            // 通过方法签名的声明类型来判断
            String declaringTypeName = joinPoint.getSignature().getDeclaringTypeName();
            if (declaringTypeName.contains("SvcSDKService")) {
                return "SvcSDKService";
            } else if (declaringTypeName.contains("CommonBroadCastService")) {
                return "CommonBroadCastService";
            } else if (declaringTypeName.contains("KafkaService")) {
                return "KafkaService";
            } else if (declaringTypeName.contains("HdztAwardServiceClient")) {
                return "HdztAwardServiceClient";
            }
        }

        return "Unknown";
    }

    /**
     * 根据服务类型获取业务标识
     *
     * @param serviceType 服务类型
     * @return 业务标识
     */
    private String getBusinessId(String serviceType) {
        switch (serviceType) {
            case "SvcSDKService":
                return "svc_sdk_monitor";
            case "CommonBroadCastService":
                return "broadcast_monitor";
            case "KafkaService":
                return "kafka_monitor";
            case "HdztAwardServiceClient":
                return "award_monitor";
            default:
                return "unknown_service_monitor";
        }
    }

    /**
     * 根据服务类型获取业务描述
     *
     * @param serviceType 服务类型
     * @return 业务描述
     */
    private String getBusinessDescription(String serviceType) {
        switch (serviceType) {
            case "SvcSDKService":
                return "SvcSDKService方法调用监控";
            case "CommonBroadCastService":
                return "CommonBroadCastService方法调用监控";
            case "KafkaService":
                return "KafkaService方法调用监控";
            case "HdztAwardServiceClient":
                return "HdztAwardServiceClient方法调用监控";
            default:
                return "未知服务方法调用监控";
        }
    }

    /**
     * 智能序列化参数，对PB对象只序列化有值的字段
     *
     * @param args 方法参数数组
     * @return 序列化后的JSON字符串
     */
    private String serializeParameters(Object[] args) {
        if (args == null || args.length == 0) {
            return "[]";
        }

        List<Object> serializedArgs = new ArrayList<>();

        for (Object arg : args) {
            if (arg == null) {
                serializedArgs.add(null);
            } else if (arg instanceof Message) {
                // 处理Protocol Buffer对象
                serializedArgs.add(serializeProtobufMessage((Message) arg));
            } else {
                // 处理普通对象
                serializedArgs.add(serializeRegularObject(arg));
            }
        }

        return JSON.toJSONString(serializedArgs);
    }

    /**
     * 序列化Protocol Buffer消息，只包含有值的字段
     *
     * @param message PB消息对象
     * @return 包含有值字段的Map
     */
    private Map<String, Object> serializeProtobufMessage(Message message) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 使用JsonFormat将PB对象转换为JSON字符串（只包含有值的字段）
            String jsonString = JsonFormat.printer()
                    .omittingInsignificantWhitespace()
                    .print(message);

            // 将JSON字符串解析为Map，以便进一步处理
            @SuppressWarnings("unchecked")
            Map<String, Object> jsonMap = JSON.parseObject(jsonString, Map.class);

            if (jsonMap != null) {
                result.putAll(jsonMap);
            }

            // 添加消息类型信息
            result.put("_messageType", message.getClass().getSimpleName());

        } catch (Exception e) {
            // 如果PB序列化失败，回退到基本信息
            result.put("_messageType", message.getClass().getSimpleName());
            result.put("_serializationError", e.getMessage());
            result.put("_toString", message.toString());
        }

        return result;
    }

    /**
     * 序列化普通对象
     *
     * @param obj 普通对象
     * @return 序列化结果
     */
    private Object serializeRegularObject(Object obj) {
        try {
            // 对于基本类型和字符串，直接返回
            if (obj instanceof String || obj instanceof Number ||
                obj instanceof Boolean || obj instanceof Character) {
                return obj;
            }

            // 对于其他对象，尝试JSON序列化
            // 但限制序列化后的字符串长度，避免过大的对象
            String jsonString = JSON.toJSONString(obj);
            if (jsonString.length() > 1000) {
                // 如果序列化结果太长，只保留类型信息和摘要
                Map<String, Object> summary = new HashMap<>();
                summary.put("_objectType", obj.getClass().getSimpleName());
                summary.put("_size", jsonString.length());
                summary.put("_summary", jsonString.substring(0, Math.min(200, jsonString.length())) + "...");
                return summary;
            }

            return JSON.parseObject(jsonString);

        } catch (Exception e) {
            // 序列化失败时返回基本信息
            Map<String, Object> fallback = new HashMap<>();
            fallback.put("_objectType", obj.getClass().getSimpleName());
            fallback.put("_serializationError", e.getMessage());
            fallback.put("_toString", obj.toString());
            return fallback;
        }
    }
    
    /**
     * 异步上报运维数据
     *
     * @param serviceType 服务类型
     * @param methodName 调用的方法名
     * @param content 参数内容（JSON序列化后的字符串）
     * @param contextInfo 组件上下文信息
     */
    private void asyncReportData(String serviceType, String methodName, String content, ComponentContextInfo contextInfo) {
        try {
            // 根据服务类型创建不同的业务标识和描述
            String businessId = getBusinessId(serviceType);
            String businessDesc = getBusinessDescription(serviceType);

            // 创建上报数据
            ActReportData reportData = ReporterClient.createManualReport(
                "hdpt",                    // 系统标识
                businessId,                // 业务标识
                businessDesc,              // 业务描述
                serviceType + "." + methodName, // method字段：服务类型.方法名
                content                    // content字段：参数的JSON序列化
            );

            // 从线程变量或上下文信息中设置actId、cmptId、cmptIndex
            if (contextInfo != null) {
                if (contextInfo.getActId() != null) {
                    reportData.setActId(contextInfo.getActId());
                }
                // 使用extInt存储cmptId，extLong存储cmptIndex
                if (contextInfo.getCmptId() != null) {
                    reportData.setCmptId(contextInfo.getCmptId());
                }
                if (contextInfo.getCmptIndex() != null) {
                    reportData.setCmptIndex(contextInfo.getCmptIndex());
                }
            }

            // 设置追踪ID
            String traceId = Span.current().getSpanContext().getTraceId();
            if (traceId != null && !traceId.isEmpty()) {
                reportData.setTraceId(traceId);
            }
            
            // 异步发送
            CompletableFuture<Boolean> future = reporterClient.sendAsync(reportData);
            
            // 添加回调处理结果（可选）
            future.whenComplete((success, throwable) -> {
                String callerInfo = contextInfo != null ? contextInfo.getCallerClassName() : "unknown";
                if (throwable != null) {
                    log.warn("运维数据上报失败，服务: {}, 方法: {}, 调用者: {}, 错误: {}",
                            serviceType, methodName, callerInfo, throwable.getMessage());
                } else if (Boolean.TRUE.equals(success)) {
                    log.debug("运维数据上报成功，服务: {}, 方法: {}, 调用者: {}, actId: {}, cmptId: {}, cmptIndex: {}",
                            serviceType, methodName, callerInfo, contextInfo != null ? contextInfo.getActId() : null,
                            contextInfo != null ? contextInfo.getCmptId() : null,
                            contextInfo != null ? contextInfo.getCmptIndex() : null);
                } else {
                    log.warn("运维数据上报返回失败，服务: {}, 方法: {}, 调用者: {}", serviceType, methodName, callerInfo);
                }
            });
            
        } catch (Exception e) {
            String callerInfo = contextInfo != null ? contextInfo.getCallerClassName() : "unknown";
            log.error("创建运维数据上报时发生异常，服务: {}, 方法: {}, 调用者: {}, 错误: {}",
                     serviceType, methodName, callerInfo, e.getMessage(), e);
        }
    }
}
