package com.yy.gameecology.activity.report;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 * 运维数据上报上下文管理类
 * 使用MDC保存上下文信息，支持多线程参数传递
 *
 * <AUTHOR>
 * @date 2025-08-05 10:46
 **/
public class ReportContext {
    private static final Logger log = LoggerFactory.getLogger(ReportContext.class);

    // ==================== MDC Key 常量定义 ====================

    // 组件上下文相关的MDC Key
    private static final String MDC_COMPONENT_ACT_ID = "report.component.actId";
    private static final String MDC_COMPONENT_CMPT_ID = "report.component.cmptId";
    private static final String MDC_COMPONENT_CMPT_INDEX = "report.component.cmptIndex";

    // 榜单上下文相关的MDC Key
    private static final String MDC_RANKING_ACT_ID = "report.ranking.actId";
    private static final String MDC_RANKING_RANK_ID = "report.ranking.rankId";
    private static final String MDC_RANKING_PHASE_ID = "report.ranking.phaseId";
    private static final String MDC_RANKING_TIME_CODE = "report.ranking.timeCode";

    /**
     * 组件上下文信息类
     */
    public static class ComponentContext {
        private Long actId;
        private Long cmptId;
        private Long cmptIndex;

        public ComponentContext() {
        }

        public ComponentContext(Long actId, Long cmptId, Long cmptIndex) {
            this.actId = actId;
            this.cmptId = cmptId;
            this.cmptIndex = cmptIndex;
        }

        public Long getActId() { return actId; }
        public void setActId(Long actId) { this.actId = actId; }

        public Long getCmptId() { return cmptId; }
        public void setCmptId(Long cmptId) { this.cmptId = cmptId; }

        public Long getCmptIndex() { return cmptIndex; }
        public void setCmptIndex(Long cmptIndex) { this.cmptIndex = cmptIndex; }

        public boolean isEmpty() {
            return actId == null && cmptId == null && cmptIndex == null;
        }

        @Override
        public String toString() {
            return String.format("ComponentContext{actId=%s, cmptId=%s, cmptIndex=%s}",
                    actId, cmptId, cmptIndex);
        }
    }

    /**
     * 榜单上下文信息类
     */
    public static class RankingContext {
        private Long actId;
        private Long rankId;
        private Long phaseId;
        private String timeCode;

        public RankingContext() {
        }

        public RankingContext(Long actId, Long rankId, Long phaseId, String timeCode) {
            this.actId = actId;
            this.rankId = rankId;
            this.phaseId = phaseId;
            this.timeCode = timeCode;
        }

        public Long getActId() { return actId; }
        public void setActId(Long actId) { this.actId = actId; }

        public Long getRankId() { return rankId; }
        public void setRankId(Long rankId) { this.rankId = rankId; }

        public Long getPhaseId() { return phaseId; }
        public void setPhaseId(Long phaseId) { this.phaseId = phaseId; }

        public String getTimeCode() { return timeCode; }
        public void setTimeCode(String timeCode) { this.timeCode = timeCode; }

        public boolean isEmpty() {
            return actId == null && rankId == null && phaseId == null && timeCode == null;
        }

        @Override
        public String toString() {
            return String.format("RankingContext{actId=%s, rankId=%s, phaseId=%s, timeCode='%s'}",
                    actId, rankId, phaseId, timeCode);
        }
    }

    // ==================== 组件上下文管理方法 ====================

    /**
     * 设置组件上下文信息到MDC
     *
     * @param actId 活动ID
     * @param cmptId 组件ID
     * @param cmptIndex 组件索引
     */
    public static void setComponentContext(Long actId, Long cmptId, Long cmptIndex) {
        if (actId != null) {
            MDC.put(MDC_COMPONENT_ACT_ID, actId.toString());
        }
        if (cmptId != null) {
            MDC.put(MDC_COMPONENT_CMPT_ID, cmptId.toString());
        }
        if (cmptIndex != null) {
            MDC.put(MDC_COMPONENT_CMPT_INDEX, cmptIndex.toString());
        }

        log.debug("设置组件上下文到MDC: actId={}, cmptId={}, cmptIndex={}", actId, cmptId, cmptIndex);
    }

    /**
     * 获取当前线程的组件上下文信息
     *
     * @return 组件上下文信息，从MDC中读取
     */
    public static ComponentContext getComponentContext() {
        Long actId = parseLong(MDC.get(MDC_COMPONENT_ACT_ID));
        Long cmptId = parseLong(MDC.get(MDC_COMPONENT_CMPT_ID));
        Long cmptIndex = parseLong(MDC.get(MDC_COMPONENT_CMPT_INDEX));

        return new ComponentContext(actId, cmptId, cmptIndex);
    }

    /**
     * 获取当前线程的活动ID（组件上下文）
     *
     * @return 活动ID，如果没有设置则返回null
     */
    public static Long getComponentActId() {
        return parseLong(MDC.get(MDC_COMPONENT_ACT_ID));
    }

    /**
     * 获取当前线程的组件ID
     *
     * @return 组件ID，如果没有设置则返回null
     */
    public static Long getComponentId() {
        return parseLong(MDC.get(MDC_COMPONENT_CMPT_ID));
    }

    /**
     * 获取当前线程的组件索引
     *
     * @return 组件索引，如果没有设置则返回null
     */
    public static Long getComponentIndex() {
        return parseLong(MDC.get(MDC_COMPONENT_CMPT_INDEX));
    }

    /**
     * 清除当前线程的组件上下文信息
     */
    public static void clearComponentContext() {
        log.debug("清除组件上下文MDC: actId={}, cmptId={}, cmptIndex={}",
                MDC.get(MDC_COMPONENT_ACT_ID), MDC.get(MDC_COMPONENT_CMPT_ID), MDC.get(MDC_COMPONENT_CMPT_INDEX));

        MDC.remove(MDC_COMPONENT_ACT_ID);
        MDC.remove(MDC_COMPONENT_CMPT_ID);
        MDC.remove(MDC_COMPONENT_CMPT_INDEX);
    }

    // ==================== 榜单上下文管理方法 ====================

    /**
     * 设置榜单上下文信息到MDC
     *
     * @param actId 活动ID
     * @param rankId 榜单ID
     * @param phaseId 阶段ID
     * @param timeCode 时间编码
     */
    public static void setRankingContext(Long actId, Long rankId, Long phaseId, String timeCode) {
        if (actId != null) {
            MDC.put(MDC_RANKING_ACT_ID, actId.toString());
        }
        if (rankId != null) {
            MDC.put(MDC_RANKING_RANK_ID, rankId.toString());
        }
        if (phaseId != null) {
            MDC.put(MDC_RANKING_PHASE_ID, phaseId.toString());
        }
        if (timeCode != null) {
            MDC.put(MDC_RANKING_TIME_CODE, timeCode);
        }

        log.debug("设置榜单上下文到MDC: actId={}, rankId={}, phaseId={}, timeCode={}",
                actId, rankId, phaseId, timeCode);
    }

    /**
     * 获取当前线程的榜单上下文信息
     *
     * @return 榜单上下文信息，从MDC中读取
     */
    public static RankingContext getRankingContext() {
        Long actId = parseLong(MDC.get(MDC_RANKING_ACT_ID));
        Long rankId = parseLong(MDC.get(MDC_RANKING_RANK_ID));
        Long phaseId = parseLong(MDC.get(MDC_RANKING_PHASE_ID));
        String timeCode = MDC.get(MDC_RANKING_TIME_CODE);

        return new RankingContext(actId, rankId, phaseId, timeCode);
    }

    /**
     * 获取当前线程的活动ID（榜单上下文）
     *
     * @return 活动ID，如果没有设置则返回null
     */
    public static Long getRankingActId() {
        return parseLong(MDC.get(MDC_RANKING_ACT_ID));
    }

    /**
     * 获取当前线程的榜单ID
     *
     * @return 榜单ID，如果没有设置则返回null
     */
    public static Long getRankId() {
        return parseLong(MDC.get(MDC_RANKING_RANK_ID));
    }

    /**
     * 获取当前线程的阶段ID
     *
     * @return 阶段ID，如果没有设置则返回null
     */
    public static Long getPhaseId() {
        return parseLong(MDC.get(MDC_RANKING_PHASE_ID));
    }

    /**
     * 获取当前线程的时间编码
     *
     * @return 时间编码，如果没有设置则返回null
     */
    public static String getTimeCode() {
        return MDC.get(MDC_RANKING_TIME_CODE);
    }

    /**
     * 清除当前线程的榜单上下文信息
     */
    public static void clearRankingContext() {
        log.debug("清除榜单上下文MDC: actId={}, rankId={}, phaseId={}, timeCode={}",
                MDC.get(MDC_RANKING_ACT_ID), MDC.get(MDC_RANKING_RANK_ID),
                MDC.get(MDC_RANKING_PHASE_ID), MDC.get(MDC_RANKING_TIME_CODE));

        MDC.remove(MDC_RANKING_ACT_ID);
        MDC.remove(MDC_RANKING_RANK_ID);
        MDC.remove(MDC_RANKING_PHASE_ID);
        MDC.remove(MDC_RANKING_TIME_CODE);
    }

    // ==================== 便利方法和全局管理 ====================

    /**
     * 清除当前线程的所有上下文信息（组件上下文和榜单上下文）
     */
    public static void clearAllContext() {
        log.debug("清除所有上下文信息");
        clearComponentContext();
        clearRankingContext();
    }

    /**
     * 获取当前线程的所有上下文信息的字符串表示
     *
     * @return 包含组件上下文和榜单上下文的字符串
     */
    public static String getAllContextInfo() {
        ComponentContext componentContext = getComponentContext();
        RankingContext rankingContext = getRankingContext();

        return String.format("ReportContext{component=%s, ranking=%s}",
                componentContext, rankingContext);
    }

    /**
     * 检查是否有任何上下文信息被设置
     *
     * @return 如果有任何上下文信息被设置则返回true，否则返回false
     */
    public static boolean hasAnyContext() {
        ComponentContext componentContext = getComponentContext();
        RankingContext rankingContext = getRankingContext();

        return !componentContext.isEmpty() || !rankingContext.isEmpty();
    }

    /**
     * 获取优先的活动ID
     * 优先级：组件上下文的actId > 榜单上下文的actId
     *
     * @return 活动ID，如果都没有设置则返回null
     */
    public static Long getPreferredActId() {
        Long componentActId = getComponentActId();
        if (componentActId != null) {
            return componentActId;
        }
        return getRankingActId();
    }

    /**
     * 获取当前MDC中所有报告相关的键值对
     *
     * @return 包含所有报告相关MDC信息的字符串
     */
    public static String getAllMDCInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("ReportMDC{");

        // 组件上下文
        String componentActId = MDC.get(MDC_COMPONENT_ACT_ID);
        String componentCmptId = MDC.get(MDC_COMPONENT_CMPT_ID);
        String componentCmptIndex = MDC.get(MDC_COMPONENT_CMPT_INDEX);

        if (componentActId != null || componentCmptId != null || componentCmptIndex != null) {
            sb.append("component={actId=").append(componentActId)
              .append(", cmptId=").append(componentCmptId)
              .append(", cmptIndex=").append(componentCmptIndex).append("}, ");
        }

        // 榜单上下文
        String rankingActId = MDC.get(MDC_RANKING_ACT_ID);
        String rankingRankId = MDC.get(MDC_RANKING_RANK_ID);
        String rankingPhaseId = MDC.get(MDC_RANKING_PHASE_ID);
        String rankingTimeCode = MDC.get(MDC_RANKING_TIME_CODE);

        if (rankingActId != null || rankingRankId != null || rankingPhaseId != null || rankingTimeCode != null) {
            sb.append("ranking={actId=").append(rankingActId)
              .append(", rankId=").append(rankingRankId)
              .append(", phaseId=").append(rankingPhaseId)
              .append(", timeCode=").append(rankingTimeCode).append("}");
        }

        sb.append("}");
        return sb.toString();
    }

    // ==================== 工具方法 ====================

    /**
     * 安全地将字符串解析为Long
     *
     * @param str 要解析的字符串
     * @return 解析后的Long值，如果解析失败则返回null
     */
    private static Long parseLong(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }
        try {
            return Long.parseLong(str.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析Long值: {}", str);
            return null;
        }
    }
}
