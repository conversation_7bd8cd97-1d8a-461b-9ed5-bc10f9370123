package com.yy.gameecology.activity.report;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 运维数据上报上下文管理类
 * 提供线程变量管理功能，用于存储和获取活动、组件、榜单等上下文信息
 *
 * <AUTHOR>
 * @date 2025-08-05 10:46
 **/
public class ReportContext {
    private static final Logger log = LoggerFactory.getLogger(ReportContext.class);

    /**
     * 组件上下文信息线程变量
     */
    private static final ThreadLocal<ComponentContext> COMPONENT_CONTEXT = new ThreadLocal<ComponentContext>() {
        @Override
        protected ComponentContext initialValue() {
            return new ComponentContext();
        }
    };

    /**
     * 榜单上下文信息线程变量
     */
    private static final ThreadLocal<RankingContext> RANKING_CONTEXT = new ThreadLocal<RankingContext>() {
        @Override
        protected RankingContext initialValue() {
            return new RankingContext();
        }
    };

    /**
     * 组件上下文信息类
     */
    public static class ComponentContext {
        private Long actId;
        private Long cmptId;
        private Long cmptIndex;

        public Long getActId() { return actId; }
        public void setActId(Long actId) { this.actId = actId; }

        public Long getCmptId() { return cmptId; }
        public void setCmptId(Long cmptId) { this.cmptId = cmptId; }

        public Long getCmptIndex() { return cmptIndex; }
        public void setCmptIndex(Long cmptIndex) { this.cmptIndex = cmptIndex; }

        public void clear() {
            this.actId = null;
            this.cmptId = null;
            this.cmptIndex = null;
        }

        @Override
        public String toString() {
            return String.format("ComponentContext{actId=%d, cmptId=%d, cmptIndex=%d}",
                    actId, cmptId, cmptIndex);
        }
    }

    /**
     * 榜单上下文信息类
     */
    public static class RankingContext {
        private Long actId;
        private Long rankId;
        private Long phaseId;
        private String timeCode;

        public Long getActId() { return actId; }
        public void setActId(Long actId) { this.actId = actId; }

        public Long getRankId() { return rankId; }
        public void setRankId(Long rankId) { this.rankId = rankId; }

        public Long getPhaseId() { return phaseId; }
        public void setPhaseId(Long phaseId) { this.phaseId = phaseId; }

        public String getTimeCode() { return timeCode; }
        public void setTimeCode(String timeCode) { this.timeCode = timeCode; }

        public void clear() {
            this.actId = null;
            this.rankId = null;
            this.phaseId = null;
            this.timeCode = null;
        }

        @Override
        public String toString() {
            return String.format("RankingContext{actId=%d, rankId=%d, phaseId=%d, timeCode='%s'}",
                    actId, rankId, phaseId, timeCode);
        }
    }

    // ==================== 组件上下文管理方法 ====================

    /**
     * 设置组件上下文信息到线程变量
     *
     * @param actId 活动ID
     * @param cmptId 组件ID
     * @param cmptIndex 组件索引
     */
    public static void setComponentContext(Long actId, Long cmptId, Long cmptIndex) {
        ComponentContext context = COMPONENT_CONTEXT.get();
        context.setActId(actId);
        context.setCmptId(cmptId);
        context.setCmptIndex(cmptIndex);

        log.debug("设置组件上下文: {}", context);
    }

    /**
     * 获取当前线程的组件上下文信息
     *
     * @return 组件上下文信息，如果没有设置则返回空的上下文对象
     */
    public static ComponentContext getComponentContext() {
        return COMPONENT_CONTEXT.get();
    }

    /**
     * 获取当前线程的活动ID（组件上下文）
     *
     * @return 活动ID，如果没有设置则返回null
     */
    public static Long getComponentActId() {
        return COMPONENT_CONTEXT.get().getActId();
    }

    /**
     * 获取当前线程的组件ID
     *
     * @return 组件ID，如果没有设置则返回null
     */
    public static Long getComponentId() {
        return COMPONENT_CONTEXT.get().getCmptId();
    }

    /**
     * 获取当前线程的组件索引
     *
     * @return 组件索引，如果没有设置则返回null
     */
    public static Long getComponentIndex() {
        return COMPONENT_CONTEXT.get().getCmptIndex();
    }

    /**
     * 清除当前线程的组件上下文信息
     */
    public static void clearComponentContext() {
        ComponentContext context = COMPONENT_CONTEXT.get();
        log.debug("清除组件上下文: {}", context);
        context.clear();
    }

    /**
     * 移除当前线程的组件上下文ThreadLocal变量
     * 注意：这会完全移除ThreadLocal变量，建议在线程结束时调用
     */
    public static void removeComponentContext() {
        log.debug("移除组件上下文ThreadLocal变量");
        COMPONENT_CONTEXT.remove();
    }

    // ==================== 榜单上下文管理方法 ====================

    /**
     * 设置榜单上下文信息到线程变量
     *
     * @param actId 活动ID
     * @param rankId 榜单ID
     * @param phaseId 阶段ID
     * @param timeCode 时间编码
     */
    public static void setRankingContext(Long actId, Long rankId, Long phaseId, String timeCode) {
        RankingContext context = RANKING_CONTEXT.get();
        context.setActId(actId);
        context.setRankId(rankId);
        context.setPhaseId(phaseId);
        context.setTimeCode(timeCode);

        log.debug("设置榜单上下文: {}", context);
    }

    /**
     * 获取当前线程的榜单上下文信息
     *
     * @return 榜单上下文信息，如果没有设置则返回空的上下文对象
     */
    public static RankingContext getRankingContext() {
        return RANKING_CONTEXT.get();
    }

    /**
     * 获取当前线程的活动ID（榜单上下文）
     *
     * @return 活动ID，如果没有设置则返回null
     */
    public static Long getRankingActId() {
        return RANKING_CONTEXT.get().getActId();
    }

    /**
     * 获取当前线程的榜单ID
     *
     * @return 榜单ID，如果没有设置则返回null
     */
    public static Long getRankId() {
        return RANKING_CONTEXT.get().getRankId();
    }

    /**
     * 获取当前线程的阶段ID
     *
     * @return 阶段ID，如果没有设置则返回null
     */
    public static Long getPhaseId() {
        return RANKING_CONTEXT.get().getPhaseId();
    }

    /**
     * 获取当前线程的时间编码
     *
     * @return 时间编码，如果没有设置则返回null
     */
    public static String getTimeCode() {
        return RANKING_CONTEXT.get().getTimeCode();
    }

    /**
     * 清除当前线程的榜单上下文信息
     */
    public static void clearRankingContext() {
        RankingContext context = RANKING_CONTEXT.get();
        log.debug("清除榜单上下文: {}", context);
        context.clear();
    }

    /**
     * 移除当前线程的榜单上下文ThreadLocal变量
     * 注意：这会完全移除ThreadLocal变量，建议在线程结束时调用
     */
    public static void removeRankingContext() {
        log.debug("移除榜单上下文ThreadLocal变量");
        RANKING_CONTEXT.remove();
    }

    // ==================== 便利方法和全局管理 ====================

    /**
     * 清除当前线程的所有上下文信息（组件上下文和榜单上下文）
     */
    public static void clearAllContext() {
        log.debug("清除所有上下文信息");
        clearComponentContext();
        clearRankingContext();
    }

    /**
     * 移除当前线程的所有ThreadLocal变量
     * 注意：这会完全移除所有ThreadLocal变量，建议在线程结束时调用
     */
    public static void removeAllContext() {
        log.debug("移除所有ThreadLocal变量");
        removeComponentContext();
        removeRankingContext();
    }

    /**
     * 获取当前线程的所有上下文信息的字符串表示
     *
     * @return 包含组件上下文和榜单上下文的字符串
     */
    public static String getAllContextInfo() {
        ComponentContext componentContext = getComponentContext();
        RankingContext rankingContext = getRankingContext();

        return String.format("ReportContext{component=%s, ranking=%s}",
                componentContext, rankingContext);
    }

    /**
     * 检查是否有任何上下文信息被设置
     *
     * @return 如果有任何上下文信息被设置则返回true，否则返回false
     */
    public static boolean hasAnyContext() {
        ComponentContext componentContext = getComponentContext();
        RankingContext rankingContext = getRankingContext();

        boolean hasComponent = componentContext.getActId() != null ||
                              componentContext.getCmptId() != null ||
                              componentContext.getCmptIndex() != null;

        boolean hasRanking = rankingContext.getActId() != null ||
                            rankingContext.getRankId() != null ||
                            rankingContext.getPhaseId() != null ||
                            rankingContext.getTimeCode() != null;

        return hasComponent || hasRanking;
    }

    /**
     * 获取优先的活动ID
     * 优先级：组件上下文的actId > 榜单上下文的actId
     *
     * @return 活动ID，如果都没有设置则返回null
     */
    public static Long getPreferredActId() {
        Long componentActId = getComponentActId();
        if (componentActId != null) {
            return componentActId;
        }
        return getRankingActId();
    }
}
