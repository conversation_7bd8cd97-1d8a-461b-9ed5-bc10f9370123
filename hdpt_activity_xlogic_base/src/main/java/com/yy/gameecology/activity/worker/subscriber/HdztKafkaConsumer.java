package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.hdzt.*;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.report.ReportContext;
import com.yy.gameecology.activity.report.ReportContextResolver;
import com.yy.gameecology.activity.service.ActService;
import com.yy.gameecology.activity.service.ActServiceManager;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.SystemUtil;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 代替原来的 HdztRabbitConsumer，处理hdzt发出的kafka消息
 *
 * <AUTHOR>
 * @date 2021年12月22日 上午11:53:26
 */
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
public class HdztKafkaConsumer {

    private Logger log = LoggerFactory.getLogger(HdztKafkaConsumer.class);

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;


    @KafkaListener(containerFactory = "hdztWxContainerFactory", id = "hdzt_wx_kafka_award_issueNotice",
            topics = "${kafka.hdzt.award.issueNotice.topic}",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onMessageAwardIssueNoticWx(ConsumerRecord<String, String> consumerRecord) {
        hdztAward("wx", consumerRecord.value());
    }

    @KafkaListener(containerFactory = "hdztSzContainerFactory", id = "hdzt_sz_kafka_award_issueNotice",
            topics = "${kafka.hdzt.award.issueNotice.topic}",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onMessageAwardIssueNoticSz(ConsumerRecord<String, String> consumerRecord) {
        hdztAward("sz", consumerRecord.value());
    }

    public void hdztAward(String from, String payload) {
        try {
            HdztAwardLotteryMsg hdztAwardLotteryMsg = JSON.parseObject(payload, HdztAwardLotteryMsg.class);
            if (hdztAwardLotteryMsg != null && hdztAwardLotteryMsg.getUid() > 0
                    && !CollectionUtils.isEmpty(hdztAwardLotteryMsg.getData())) {

                long actId = hdztAwardLotteryMsg.getActId();
                if (actId == 0) {
                    throw new RuntimeException("中台需要添加 actId");
                }

                // 发奖消息防止重复消费 - added by guoliping / 2020-10-28
                long taskId = hdztAwardLotteryMsg.getTaskId();
                String key = Const.addActivityPrefix(actId, String.format(Const.HDZT_AWARD_DUPLICATED_CHECK_KEY, taskId));
                String value = DateUtil.today() + ", " + SystemUtil.getWorkerBrief() + ":" + this.getClass().getSimpleName();
                if (!actRedisGroupDao.hsetnx(redisConfigManager.getGroupCode(actId), key, hdztAwardLotteryMsg.getSeq(), value)) {
                    log.error("hdztAward skip@duplicated message:{}", payload);
                    return;
                }

                //TODO 此代码等老活动全部结束后可以删除，改用组件方式
                // 原来的事件处理异常捕获，以便不影响下面的 hdzj 处理 - modified by guoliping / 2021-04-13
//                try {
//                    ActService actService = actServiceManager.getActService(hdztAwardLotteryMsg.getActId());
//                    if (actService != null) {
//                        actService.onLotteryAwardEvent(hdztAwardLotteryMsg);
//                    }
//                } catch (Throwable t) {
//                    log.error("hdztAward exception@payload:{}, err:{}", payload, t.getMessage(), t);
//                }

                // 活动组件事件分发处理 - added by guoliping / 2021-04-13
                hdzjEventDispatcher.notify(hdztAwardLotteryMsg);
            }
            log.info("hdztAward ok event:{}", payload);
        } catch (Exception e) {
            log.error("hdztAward err:{} event:{}", e.getMessage(), payload, e);
        }
    }


    @KafkaListener(containerFactory = "hdztWxContainerFactory", id = "hdzt_wx_kafka_ranking_events",
            topics = "${kafka.hdzt.ranking.events.topic}",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onMessageRankingEventsWx(ConsumerRecord<String, String> consumerRecord) {
        hdztActivityMessage("wx", consumerRecord.value());
    }

    @KafkaListener(containerFactory = "hdztSzContainerFactory", id = "hdzt_sz_kafka_ranking_events",
            topics = "${kafka.hdzt.ranking.events.topic}",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onMessageRankingEventsSz(ConsumerRecord<String, String> consumerRecord) {
        hdztActivityMessage("sz", consumerRecord.value());
    }

    @SuppressWarnings({"unused", "rawtypes"})
    public void hdztActivityMessage(String from, String payload) {
        try {
            int pos = payload.indexOf("|");
            if (pos == -1) {
                log.error("onMessage fail@from:{}, invalid message format, pos:{}, payload:{}", from, pos, payload);
                return;
            }


            log.info("onMessage info @from:{}, message format, pos:{}, payload:{}", from, pos, payload);
            long uri = Convert.toLong(payload.substring(0, pos), -1);
            String data = payload.substring(pos + 1);
            //事件分发
            dispatcher(from, uri, data);

        } catch (Throwable t) {
            log.error("onMessage exception@from:{}, err:{}, event:{}", from, t.getMessage(), payload, t);
        }
    }

    /**
     * @param uri
     * @param data
     * @throws Exception
     */
    private void dispatcher(String from, Long uri, String data) throws Exception {

        Class zclass = null;
        if (uri == BaseEvent.ACTIVITY_TIME_START) {
            zclass = ActivityTimeStart.class;

        } else if (uri == BaseEvent.ACTIVITY_TIME_END) {
            zclass = ActivityTimeEnd.class;

        } else if (uri == BaseEvent.RANKING_TIME_START) {
            zclass = RankingTimeStart.class;

        } else if (uri == BaseEvent.RANKING_TIME_END) {
            zclass = RankingTimeEnd.class;

        } else if (uri == BaseEvent.PHASE_TIME_START) {
            zclass = PhaseTimeStart.class;

        } else if (uri == BaseEvent.PHASE_TIME_END) {
            zclass = PhaseTimeEnd.class;

        } else if (uri == BaseEvent.PK_SETTLE_TIME_END) {
            zclass = PkSettleTimeEnd.class;

        } else if (uri == BaseEvent.PROMOT_TIME_END) {
            zclass = PromotTimeEnd.class;

        } else if (uri == BaseEvent.TASK_PROGRESS_CHANGED) {
            zclass = TaskProgressChanged.class;

        } else if (uri == BaseEvent.REPEAT_TOP_TASK_CHANGED) {
            zclass = RepeatTopTaskChanged.class;

        } else if (uri == BaseEvent.RANKING_SCORE_CHANGED) {
            zclass = RankingScoreChanged.class;
        } else if (uri == BaseEvent.MEMBER_SCORE_CHANGED) {
            zclass = MemberScoreChanged.class;
        } else {
            throw new Exception("事件uri无法识别，uri={}" + uri);
        }

        // 消息实例重复检查（某些情况消息源头会重复发送相同消息实例，这里要主动过滤掉！）- added by guoliping / 2021-01-12
        BaseEvent event = (BaseEvent) (JSON.parseObject(data, zclass));
        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        String key = Const.addActivityPrefix(event.getActId(), String.format(Const.HDZT_EVENT_DUPLICATED_CHECK_KEY, zclass.getSimpleName()));
        String value = DateUtil.today() + ", " + SystemUtil.getWorkerBrief() + ":" + this.getClass().getSimpleName();
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();

        // 空ekey时，一般是动态行为数据，请求量会比较大，需要更快过期防重seq key，防止过量占用内存
        if (StringUtil.isBlank(event.getEkey())) {
            String seqKey = key + ":" + seq;
            // 参考 com.yy.hdzt.common.consts.Const#NOTIFY_SCAN_SPAN_SECONDS = 15分钟, 这里设 60 分钟, 已足够长，防中台调整导致不能覆盖（中台按常理不超1小时，因重试太久也无意义）
            // 只保留1小时
            final long oneHourSecs = 3600;
            if (!actRedisGroupDao.setNX(groupCode, seqKey, value, oneHourSecs)) {
                log.warn("dispatcher skip1@from:{}, duplicated message, seqKey:{}, uri:{}, data:{}", from, seqKey, uri, data);
                return;
            }
        } else {
            if (!actRedisGroupDao.hsetnx(groupCode, key, seq, value)) {
                log.warn("dispatcher skip2@from:{}, duplicated message, key:{}, uri:{}, field:{}, data:{}", from, key, uri, seq, data);
                return;
            }
            //30天过期够久了
            actRedisGroupDao.setExpire(groupCode, key, 3600 * 24 * 30);
        }

        // 原来的事件处理异常捕获，以便不影响下面的 hdzj 处理 - modified by guoliping / 2021-04-13
//        try {
//            publisher.publishEvent(event);
//        } catch (Throwable t) {
//            log.error("publishEvent exception@from:{}, data:{}, uri:{}, err:{}", from, data, uri, t.getMessage(), t);
//        }

        // 活动组件事件分发处理 - added by guoliping / 2021-04-13

        ReportContextResolver.resolveAndSetRankingContext(event);

        hdzjEventDispatcher.notify(event);

        ReportContext.clearRankingContext();

    }
}
