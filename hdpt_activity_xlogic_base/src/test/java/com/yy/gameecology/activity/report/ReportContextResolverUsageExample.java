package com.yy.gameecology.activity.report;

import com.yy.gameecology.activity.bean.hdzt.BaseEvent;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeStart;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ReportContextResolver使用示例
 * 展示如何在实际业务中使用ReportContextResolver来解析BaseEvent参数
 * 
 * <AUTHOR> Generated
 * @date 2025-01-04
 */
public class ReportContextResolverUsageExample {
    
    private static final Logger log = LoggerFactory.getLogger(ReportContextResolverUsageExample.class);
    
    /**
     * 示例1：处理阶段结束事件
     */
    public static void handlePhaseTimeEndEvent() {
        log.info("=== 处理阶段结束事件示例 ===");
        
        try {
            // 模拟接收到阶段结束事件
            PhaseTimeEnd event = new PhaseTimeEnd();
            event.setActId(2025010401L);
            event.setRankId(1001L);
            event.setPhaseId(2001L);
            event.setTimeKey(1L); // 按日分榜
            event.setEndTime("2025-01-04 23:59:59");
            
            log.info("接收到阶段结束事件: {}", ReportContextResolver.getFieldSummary(event));
            
            // 验证事件有效性
            ReportContextResolver.ValidationResult validation = ReportContextResolver.validateEvent(event);
            if (!validation.isValid()) {
                log.warn("事件验证失败: {}", validation.getMessage());
                return;
            }
            
            // 解析并设置上下文
            boolean success = ReportContextResolver.resolveAndSetRankingContext(event);
            if (success) {
                log.info("成功设置榜单上下文");
                
                // 获取设置的上下文信息
                ReportContext.RankingContext context = ReportContext.getRankingContext();
                log.info("当前榜单上下文: {}", context);
                
                // 执行业务逻辑
                performPhaseEndBusinessLogic();
                
            } else {
                log.error("设置榜单上下文失败");
            }
            
        } finally {
            // 清理上下文
            ReportContext.clearRankingContext();
            log.info("阶段结束事件处理完成，上下文已清理");
        }
    }
    
    /**
     * 示例2：处理阶段开始事件
     */
    public static void handlePhaseTimeStartEvent() {
        log.info("=== 处理阶段开始事件示例 ===");
        
        try {
            // 模拟接收到阶段开始事件
            PhaseTimeStart event = new PhaseTimeStart();
            event.setActId(2025010402L);
            event.setRankId(1002L);
            event.setPhaseId(2002L);
            event.setTimeKey(2L); // 按小时分榜
            event.setStartTime("2025-01-04 10:00:00");
            
            log.info("接收到阶段开始事件: {}", ReportContextResolver.getFieldSummary(event));
            
            // 检查事件是否包含必要字段
            boolean hasPhaseId = ReportContextResolver.hasField(event, "phaseId");
            boolean hasTimeKey = ReportContextResolver.hasField(event, "timeKey");
            boolean hasEndTime = ReportContextResolver.hasField(event, "endTime");
            
            log.info("字段检查结果: phaseId={}, timeKey={}, endTime={}", hasPhaseId, hasTimeKey, hasEndTime);
            
            // 解析并设置上下文
            boolean success = ReportContextResolver.resolveAndSetRankingContext(event);
            if (success) {
                log.info("成功设置榜单上下文");
                
                // 由于PhaseTimeStart没有endTime，timeCode会为null
                ReportContext.RankingContext context = ReportContext.getRankingContext();
                log.info("当前榜单上下文: {}", context);
                
                if (context.getTimeCode() == null) {
                    log.info("timeCode为null，这是正常的，因为PhaseTimeStart没有endTime字段");
                }
                
                // 执行业务逻辑
                performPhaseStartBusinessLogic();
                
            } else {
                log.error("设置榜单上下文失败");
            }
            
        } finally {
            // 清理上下文
            ReportContext.clearRankingContext();
            log.info("阶段开始事件处理完成，上下文已清理");
        }
    }
    
    /**
     * 示例3：处理晋级结束事件
     */
    public static void handlePromotTimeEndEvent() {
        log.info("=== 处理晋级结束事件示例 ===");
        
        try {
            // 模拟接收到晋级结束事件
            PromotTimeEnd event = new PromotTimeEnd();
            event.setActId(2025010403L);
            event.setRankId(1003L);
            event.setPhaseId(2003L);
            event.setTimeKey(2L); // 按小时分榜
            event.setEndTime("2025-01-04 15:59:59");
            
            log.info("接收到晋级结束事件: {}", ReportContextResolver.getFieldSummary(event));
            
            // 单独提取各个参数
            Long actId = ReportContextResolver.extractActId(event);
            Long rankId = ReportContextResolver.extractRankId(event);
            Long phaseId = ReportContextResolver.extractPhaseId(event);
            String timeCode = ReportContextResolver.extractTimeCode(event);
            
            log.info("提取的参数: actId={}, rankId={}, phaseId={}, timeCode={}", 
                    actId, rankId, phaseId, timeCode);
            
            // 解析并设置上下文
            boolean success = ReportContextResolver.resolveAndSetRankingContext(event);
            if (success) {
                log.info("成功设置榜单上下文");
                
                // 执行业务逻辑
                performPromotEndBusinessLogic();
                
            } else {
                log.error("设置榜单上下文失败");
            }
            
        } finally {
            // 清理上下文
            ReportContext.clearRankingContext();
            log.info("晋级结束事件处理完成，上下文已清理");
        }
    }
    
    /**
     * 示例4：批量处理多个事件
     */
    public static void handleMultipleEvents() {
        log.info("=== 批量处理多个事件示例 ===");
        
        try {
            // 创建多个不同类型的事件
            PhaseTimeEnd event1 = new PhaseTimeEnd();
            event1.setActId(2025010404L);
            event1.setRankId(1004L);
            event1.setPhaseId(2004L);
            event1.setTimeKey(1L);
            event1.setEndTime("2025-01-04 23:59:59");
            
            PhaseTimeStart event2 = new PhaseTimeStart();
            event2.setActId(2025010405L);
            event2.setRankId(1005L);
            event2.setPhaseId(2005L);
            event2.setTimeKey(2L);
            event2.setStartTime("2025-01-05 10:00:00");
            
            BaseEvent event3 = new BaseEvent(BaseEvent.ACTIVITY_TIME_START);
            event3.setActId(2025010406L);
            event3.setRankId(1006L);
            
            log.info("准备批量处理3个事件");
            
            // 逐个处理事件
            BaseEvent[] events = {event1, event2, event3};
            for (int i = 0; i < events.length; i++) {
                BaseEvent event = events[i];
                log.info("处理事件{}: {}", i + 1, ReportContextResolver.getFieldSummary(event));
                
                // 验证事件
                ReportContextResolver.ValidationResult validation = ReportContextResolver.validateEvent(event);
                log.info("事件{}验证结果: {}", i + 1, validation);
                
                if (validation.isValid()) {
                    // 设置上下文并执行业务逻辑
                    boolean success = ReportContextResolver.resolveAndSetRankingContext(event);
                    if (success) {
                        log.info("事件{}上下文设置成功", i + 1);
                        performEventSpecificBusinessLogic(event, i + 1);
                    }
                } else {
                    log.warn("事件{}验证失败，跳过处理", i + 1);
                }
                
                // 清理上下文，为下一个事件做准备
                ReportContext.clearRankingContext();
            }
            
            // 也可以使用批量处理方法
            int successCount = ReportContextResolver.batchResolveAndSetRankingContext(events);
            log.info("批量处理结果: 成功处理{}个事件", successCount);
            
        } finally {
            // 确保清理所有上下文
            ReportContext.clearAllContext();
            log.info("批量处理完成，所有上下文已清理");
        }
    }
    
    /**
     * 示例5：错误处理和边界情况
     */
    public static void handleErrorCases() {
        log.info("=== 错误处理和边界情况示例 ===");
        
        // 处理null事件
        log.info("测试null事件处理");
        boolean nullResult = ReportContextResolver.resolveAndSetRankingContext(null);
        log.info("null事件处理结果: {}", nullResult);
        
        // 处理缺少必要字段的事件
        log.info("测试缺少必要字段的事件");
        BaseEvent incompleteEvent = new BaseEvent(BaseEvent.ACTIVITY_TIME_START);
        incompleteEvent.setActId(0L); // 无效的actId
        incompleteEvent.setRankId(0L); // 无效的rankId
        
        ReportContextResolver.ValidationResult validation = ReportContextResolver.validateEvent(incompleteEvent);
        log.info("不完整事件验证结果: {}", validation);
        
        boolean incompleteResult = ReportContextResolver.resolveAndSetRankingContext(incompleteEvent);
        log.info("不完整事件处理结果: {}", incompleteResult);
        
        // 处理时间格式错误的事件
        log.info("测试时间格式错误的事件");
        PhaseTimeEnd badTimeEvent = new PhaseTimeEnd();
        badTimeEvent.setActId(2025010407L);
        badTimeEvent.setRankId(1007L);
        badTimeEvent.setPhaseId(2007L);
        badTimeEvent.setTimeKey(1L);
        badTimeEvent.setEndTime("invalid-time-format"); // 错误的时间格式
        
        String badTimeCode = ReportContextResolver.extractTimeCode(badTimeEvent);
        log.info("错误时间格式的timeCode提取结果: {}", badTimeCode);
        
        log.info("错误处理测试完成");
    }
    
    /**
     * 模拟阶段结束业务逻辑
     */
    private static void performPhaseEndBusinessLogic() {
        log.info("执行阶段结束业务逻辑...");
        // 这里可以调用其他业务方法，它们可以通过ReportContext获取上下文信息
        // 例如：发送通知、更新数据库、触发其他事件等
    }
    
    /**
     * 模拟阶段开始业务逻辑
     */
    private static void performPhaseStartBusinessLogic() {
        log.info("执行阶段开始业务逻辑...");
        // 业务逻辑实现
    }
    
    /**
     * 模拟晋级结束业务逻辑
     */
    private static void performPromotEndBusinessLogic() {
        log.info("执行晋级结束业务逻辑...");
        // 业务逻辑实现
    }
    
    /**
     * 模拟特定事件的业务逻辑
     */
    private static void performEventSpecificBusinessLogic(BaseEvent event, int eventNumber) {
        log.info("执行事件{}的特定业务逻辑: {}", eventNumber, event.getClass().getSimpleName());
        // 根据事件类型执行不同的业务逻辑
    }
    
    /**
     * 运行所有示例
     */
    public static void runAllExamples() {
        log.info("开始运行ReportContextResolver使用示例");
        
        try {
            handlePhaseTimeEndEvent();
            Thread.sleep(500);
            
            handlePhaseTimeStartEvent();
            Thread.sleep(500);
            
            handlePromotTimeEndEvent();
            Thread.sleep(500);
            
            handleMultipleEvents();
            Thread.sleep(500);
            
            handleErrorCases();
            
        } catch (InterruptedException e) {
            log.error("示例执行被中断", e);
        } finally {
            // 确保所有上下文都被清理
            ReportContext.clearAllContext();
            log.info("所有ReportContextResolver使用示例执行完成");
        }
    }
    
    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        runAllExamples();
    }
}
