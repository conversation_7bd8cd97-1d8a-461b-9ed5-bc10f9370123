package com.yy.gameecology.activity.worker.aspect;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.hd.qly.client.ReporterClient;
import com.yy.protocol.pb.GameecologyActivity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Component;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * SvcSDKService运维数据上报切面测试类
 * 
 * <AUTHOR> Generated
 * @date 2025-01-04
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(locations = "classpath:env/dev/application.properties")
public class SvcSDKServiceReportAspectTest {
    
    private static final Logger log = LoggerFactory.getLogger(SvcSDKServiceReportAspectTest.class);
    
    @Autowired(required = false)
    private SvcSDKService svcSDKService;
    
    @Autowired(required = false)
    private ReporterClient reporterClient;
    
    @Autowired(required = false)
    private TestComponent testComponent;
    
    /**
     * 测试组件类，继承BaseActComponent
     */
    @Component
    public static class TestComponent extends BaseActComponent<TestComponentAttr> {
        
        @Autowired(required = false)
        private SvcSDKService svcSDKService;
        
        @Override
        public Long getComponentId() {
            return 999999L; // 测试用的组件ID
        }
        
        /**
         * 测试方法：调用SvcSDKService的方法
         */
        public void testSvcSDKCall() {
            if (svcSDKService != null) {
                // 创建测试消息
                GameecologyActivity.GameEcologyMsg.Builder builder = 
                    GameecologyActivity.GameEcologyMsg.newBuilder();
                builder.setUri(12345);
                builder.setData("test data");
                GameecologyActivity.GameEcologyMsg message = builder.build();
                
                // 调用SvcSDKService方法，这应该触发我们的切面
                svcSDKService.unicastUid(1001L, message);
                log.info("测试调用SvcSDKService.unicastUid完成");
            } else {
                log.warn("SvcSDKService未注入，跳过测试");
            }
        }
    }
    
    /**
     * 测试组件属性类
     */
    public static class TestComponentAttr extends ComponentAttr {
        // 测试用的组件属性类
    }
    
    @Test
    public void testSvcSDKServiceReportAspect() {
        log.info("开始测试SvcSDKService运维数据上报切面");
        
        // 检查依赖是否注入
        if (reporterClient == null) {
            log.warn("ReporterClient未注入，切面可能无法正常工作");
        } else {
            log.info("ReporterClient已注入，切面应该能正常工作");
        }
        
        if (svcSDKService == null) {
            log.warn("SvcSDKService未注入，无法进行测试");
            return;
        }
        
        if (testComponent == null) {
            log.warn("TestComponent未注入，无法进行测试");
            return;
        }
        
        try {
            // 通过测试组件调用SvcSDKService方法
            testComponent.testSvcSDKCall();
            
            // 等待一段时间让异步上报完成
            Thread.sleep(1000);
            
            log.info("测试完成，请检查日志中是否有运维数据上报的相关信息");
            
        } catch (Exception e) {
            log.error("测试过程中发生异常", e);
        }
    }
    
    @Test
    public void testDirectSvcSDKCall() {
        log.info("开始测试直接调用SvcSDKService（不通过BaseActComponent）");
        
        if (svcSDKService == null) {
            log.warn("SvcSDKService未注入，无法进行测试");
            return;
        }
        
        try {
            // 直接调用SvcSDKService方法，这不应该触发我们的切面
            GameecologyActivity.GameEcologyMsg.Builder builder = 
                GameecologyActivity.GameEcologyMsg.newBuilder();
            builder.setUri(54321);
            builder.setData("direct test data");
            GameecologyActivity.GameEcologyMsg message = builder.build();
            
            svcSDKService.unicastUid(2002L, message);
            log.info("直接调用SvcSDKService.unicastUid完成，这不应该触发运维数据上报");
            
            // 等待一段时间
            Thread.sleep(1000);
            
        } catch (Exception e) {
            log.error("直接调用测试过程中发生异常", e);
        }
    }
}
