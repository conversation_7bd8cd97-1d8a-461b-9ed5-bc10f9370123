package com.yy.gameecology.activity.report;

import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ReportContext使用示例
 * 展示如何在实际业务中使用ReportContext来管理线程变量
 * 
 * <AUTHOR> Generated
 * @date 2025-01-04
 */
@Component
public class ReportContextUsageExample extends BaseActComponent<ReportContextUsageExample.ExampleComponentAttr> {
    
    private static final Logger log = LoggerFactory.getLogger(ReportContextUsageExample.class);
    
    @Autowired(required = false)
    private SvcSDKService svcSDKService;
    
    @Override
    public Long getComponentId() {
        return 888888L; // 示例组件ID
    }
    
    /**
     * 示例组件属性类
     */
    public static class ExampleComponentAttr extends ComponentAttr {
        // 示例组件属性
    }
    
    /**
     * 示例方法1：组件上下文使用场景
     * 在组件方法中设置上下文信息，然后调用SvcSDKService
     */
    public void exampleComponentContextUsage() {
        log.info("=== 组件上下文使用示例 ===");
        
        try {
            // 1. 设置组件上下文信息到线程变量
            Long actId = 2025010401L;
            Long cmptId = getComponentId();
            Long cmptIndex = 1L;
            
            ReportContext.setComponentContext(actId, cmptId, cmptIndex);
            log.info("设置组件上下文: actId={}, cmptId={}, cmptIndex={}", actId, cmptId, cmptIndex);
            
            // 2. 调用SvcSDKService方法
            // 切面会自动从ReportContext获取上下文信息并上报
            if (svcSDKService != null) {
                GameecologyActivity.GameEcologyMsg.Builder builder = 
                    GameecologyActivity.GameEcologyMsg.newBuilder();
                builder.setUri(12345);
//                builder.setData("组件上下文示例数据");
                builder.setSeq(98765L);
                GameecologyActivity.GameEcologyMsg message = builder.build();
                
                svcSDKService.unicastUid(1001L, message);
                log.info("调用SvcSDKService.unicastUid完成，上下文信息已自动上报");
            }
            
            // 3. 验证上下文信息
            ReportContext.ComponentContext context = ReportContext.getComponentContext();
            log.info("当前组件上下文: {}", context);
            
        } finally {
            // 4. 清理上下文信息（可选，根据业务需要）
            ReportContext.clearComponentContext();
            log.info("组件上下文已清理");
        }
    }
    
    /**
     * 示例方法2：榜单上下文使用场景
     * 在榜单相关操作中设置上下文信息
     */
    public void exampleRankingContextUsage() {
        log.info("=== 榜单上下文使用示例 ===");
        
        try {
            // 1. 设置榜单上下文信息到线程变量
            Long actId = 2025010402L;
            Long rankId = 1001L;
            Long phaseId = 2001L;
            String timeCode = "20250104";
            
            ReportContext.setRankingContext(actId, rankId, phaseId, timeCode);
            log.info("设置榜单上下文: actId={}, rankId={}, phaseId={}, timeCode={}", 
                    actId, rankId, phaseId, timeCode);
            
            // 2. 模拟榜单相关的SvcSDKService调用
            if (svcSDKService != null) {
                GameecologyActivity.GameEcologyMsg.Builder builder = 
                    GameecologyActivity.GameEcologyMsg.newBuilder();
                builder.setUri(54321);
//                builder.setData("榜单上下文示例数据");
                GameecologyActivity.GameEcologyMsg message = builder.build();
                
                svcSDKService.broadcastTop(2002L, message);
                log.info("调用SvcSDKService.broadcastTop完成，榜单上下文信息可用于业务分析");
            }
            
            // 3. 验证上下文信息
            ReportContext.RankingContext context = ReportContext.getRankingContext();
            log.info("当前榜单上下文: {}", context);
            
        } finally {
            // 4. 清理上下文信息
            ReportContext.clearRankingContext();
            log.info("榜单上下文已清理");
        }
    }
    
    /**
     * 示例方法3：混合上下文使用场景
     * 同时使用组件上下文和榜单上下文
     */
    public void exampleMixedContextUsage() {
        log.info("=== 混合上下文使用示例 ===");
        
        try {
            // 1. 设置组件上下文
            ReportContext.setComponentContext(2025010403L, getComponentId(), 2L);
            
            // 2. 设置榜单上下文
            ReportContext.setRankingContext(2025010404L, 1002L, 2002L, "20250105");
            
            log.info("设置混合上下文完成");
            log.info("所有上下文信息: {}", ReportContext.getAllContextInfo());
            log.info("优先活动ID: {}", ReportContext.getPreferredActId());
            
            // 3. 调用SvcSDKService方法
            if (svcSDKService != null) {
                GameecologyActivity.GameEcologyMsg.Builder builder = 
                    GameecologyActivity.GameEcologyMsg.newBuilder();
                builder.setUri(11111);
                builder.setData("混合上下文示例数据");
                GameecologyActivity.GameEcologyMsg message = builder.build();
                
                svcSDKService.unicastUid(3003L, message);
                log.info("调用SvcSDKService.unicastUid完成，混合上下文信息已上报");
            }
            
        } finally {
            // 4. 清理所有上下文信息
            ReportContext.clearAllContext();
            log.info("所有上下文已清理");
        }
    }
    
    /**
     * 示例方法4：嵌套方法调用场景
     * 展示在嵌套方法调用中上下文信息的传递
     */
    public void exampleNestedMethodUsage() {
        log.info("=== 嵌套方法调用示例 ===");
        
        // 在外层方法设置上下文
        ReportContext.setComponentContext(2025010405L, getComponentId(), 3L);
        log.info("外层方法设置上下文完成");
        
        try {
            // 调用内层方法
            innerMethod();
            
        } finally {
            ReportContext.clearComponentContext();
            log.info("外层方法清理上下文完成");
        }
    }
    
    /**
     * 内层方法，可以直接使用外层设置的上下文信息
     */
    private void innerMethod() {
        log.info("内层方法开始执行");
        
        // 直接获取外层设置的上下文信息
        ReportContext.ComponentContext context = ReportContext.getComponentContext();
        log.info("内层方法获取到的上下文: {}", context);
        
        // 调用SvcSDKService，切面会自动使用上下文信息
        if (svcSDKService != null) {
            GameecologyActivity.GameEcologyMsg.Builder builder = 
                GameecologyActivity.GameEcologyMsg.newBuilder();
            builder.setUri(22222);
            builder.setData("嵌套方法调用示例数据");
            GameecologyActivity.GameEcologyMsg message = builder.build();
            
            svcSDKService.unicastUid(4004L, message);
            log.info("内层方法调用SvcSDKService完成");
        }
    }
    
    /**
     * 示例方法5：异常处理场景
     * 展示在异常情况下如何正确清理上下文
     */
    public void exampleExceptionHandling() {
        log.info("=== 异常处理示例 ===");
        
        try {
            // 设置上下文
            ReportContext.setComponentContext(2025010406L, getComponentId(), 4L);
            log.info("设置上下文完成");
            
            // 模拟可能抛出异常的操作
            if (Math.random() > 0.5) {
                throw new RuntimeException("模拟业务异常");
            }
            
            // 正常的SvcSDKService调用
            if (svcSDKService != null) {
                GameecologyActivity.GameEcologyMsg.Builder builder = 
                    GameecologyActivity.GameEcologyMsg.newBuilder();
                builder.setUri(33333);
                builder.setData("异常处理示例数据");
                GameecologyActivity.GameEcologyMsg message = builder.build();
                
                svcSDKService.unicastUid(5005L, message);
                log.info("正常调用SvcSDKService完成");
            }
            
        } catch (Exception e) {
            log.warn("业务执行过程中发生异常: {}", e.getMessage());
            
            // 即使发生异常，也要确保上下文被正确清理
            
        } finally {
            // 无论是否发生异常，都要清理上下文
            ReportContext.clearComponentContext();
            log.info("异常处理示例：上下文已清理");
        }
    }
    
    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        log.info("开始运行ReportContext使用示例");
        
        try {
            exampleComponentContextUsage();
            Thread.sleep(500);
            
            exampleRankingContextUsage();
            Thread.sleep(500);
            
            exampleMixedContextUsage();
            Thread.sleep(500);
            
            exampleNestedMethodUsage();
            Thread.sleep(500);
            
            exampleExceptionHandling();
            
        } catch (InterruptedException e) {
            log.error("示例执行被中断", e);
        } finally {
            // 确保所有上下文都被清理
            ReportContext.removeAllContext();
            log.info("所有示例执行完成，上下文已清理");
        }
    }
}
