package com.yy.gameecology.activity.report;

import org.junit.After;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.Assert.*;

/**
 * ReportContext测试类
 * 测试线程变量的设置、获取和清理功能
 * 
 * <AUTHOR> Generated
 * @date 2025-01-04
 */
public class ReportContextTest {
    
    private static final Logger log = LoggerFactory.getLogger(ReportContextTest.class);
    
    @After
    public void tearDown() {
        // 每个测试后清理线程变量
        ReportContext.removeAllContext();
    }
    
    @Test
    public void testComponentContext() {
        log.info("开始测试组件上下文功能");
        
        // 测试初始状态
        ReportContext.ComponentContext context = ReportContext.getComponentContext();
        assertNotNull("组件上下文不应该为null", context);
        assertNull("初始actId应该为null", context.getActId());
        assertNull("初始cmptId应该为null", context.getCmptId());
        assertNull("初始cmptIndex应该为null", context.getCmptIndex());
        
        // 测试设置组件上下文
        Long testActId = 2025010401L;
        Long testCmptId = 999999L;
        Long testCmptIndex = 1L;
        
        ReportContext.setComponentContext(testActId, testCmptId, testCmptIndex);
        
        // 验证设置结果
        assertEquals("actId应该正确设置", testActId, ReportContext.getComponentActId());
        assertEquals("cmptId应该正确设置", testCmptId, ReportContext.getComponentId());
        assertEquals("cmptIndex应该正确设置", testCmptIndex, ReportContext.getComponentIndex());
        
        // 验证上下文对象
        ReportContext.ComponentContext updatedContext = ReportContext.getComponentContext();
        assertEquals("上下文actId应该正确", testActId, updatedContext.getActId());
        assertEquals("上下文cmptId应该正确", testCmptId, updatedContext.getCmptId());
        assertEquals("上下文cmptIndex应该正确", testCmptIndex, updatedContext.getCmptIndex());
        
        log.info("组件上下文设置成功: {}", updatedContext);
        
        // 测试清除功能
        ReportContext.clearComponentContext();
        ReportContext.ComponentContext clearedContext = ReportContext.getComponentContext();
        assertNull("清除后actId应该为null", clearedContext.getActId());
        assertNull("清除后cmptId应该为null", clearedContext.getCmptId());
        assertNull("清除后cmptIndex应该为null", clearedContext.getCmptIndex());
        
        log.info("组件上下文测试完成");
    }
    
    @Test
    public void testRankingContext() {
        log.info("开始测试榜单上下文功能");
        
        // 测试初始状态
        ReportContext.RankingContext context = ReportContext.getRankingContext();
        assertNotNull("榜单上下文不应该为null", context);
        assertNull("初始actId应该为null", context.getActId());
        assertNull("初始rankId应该为null", context.getRankId());
        assertNull("初始phaseId应该为null", context.getPhaseId());
        assertNull("初始timeCode应该为null", context.getTimeCode());
        
        // 测试设置榜单上下文
        Long testActId = 2025010402L;
        Long testRankId = 1001L;
        Long testPhaseId = 2001L;
        String testTimeCode = "20250104";
        
        ReportContext.setRankingContext(testActId, testRankId, testPhaseId, testTimeCode);
        
        // 验证设置结果
        assertEquals("actId应该正确设置", testActId, ReportContext.getRankingActId());
        assertEquals("rankId应该正确设置", testRankId, ReportContext.getRankId());
        assertEquals("phaseId应该正确设置", testPhaseId, ReportContext.getPhaseId());
        assertEquals("timeCode应该正确设置", testTimeCode, ReportContext.getTimeCode());
        
        // 验证上下文对象
        ReportContext.RankingContext updatedContext = ReportContext.getRankingContext();
        assertEquals("上下文actId应该正确", testActId, updatedContext.getActId());
        assertEquals("上下文rankId应该正确", testRankId, updatedContext.getRankId());
        assertEquals("上下文phaseId应该正确", testPhaseId, updatedContext.getPhaseId());
        assertEquals("上下文timeCode应该正确", testTimeCode, updatedContext.getTimeCode());
        
        log.info("榜单上下文设置成功: {}", updatedContext);
        
        // 测试清除功能
        ReportContext.clearRankingContext();
        ReportContext.RankingContext clearedContext = ReportContext.getRankingContext();
        assertNull("清除后actId应该为null", clearedContext.getActId());
        assertNull("清除后rankId应该为null", clearedContext.getRankId());
        assertNull("清除后phaseId应该为null", clearedContext.getPhaseId());
        assertNull("清除后timeCode应该为null", clearedContext.getTimeCode());
        
        log.info("榜单上下文测试完成");
    }
    
    @Test
    public void testMixedContext() {
        log.info("开始测试混合上下文功能");
        
        // 同时设置组件上下文和榜单上下文
        ReportContext.setComponentContext(2025010403L, 888888L, 2L);
        ReportContext.setRankingContext(2025010404L, 1002L, 2002L, "20250105");
        
        // 验证两个上下文都正确设置
        assertEquals("组件actId应该正确", Long.valueOf(2025010403L), ReportContext.getComponentActId());
        assertEquals("榜单actId应该正确", Long.valueOf(2025010404L), ReportContext.getRankingActId());
        
        // 测试优先actId获取（组件上下文优先）
        assertEquals("优先actId应该是组件上下文的actId", Long.valueOf(2025010403L), ReportContext.getPreferredActId());
        
        // 测试hasAnyContext
        assertTrue("应该检测到有上下文信息", ReportContext.hasAnyContext());
        
        // 测试getAllContextInfo
        String allContextInfo = ReportContext.getAllContextInfo();
        assertNotNull("所有上下文信息不应该为null", allContextInfo);
        assertTrue("应该包含组件上下文信息", allContextInfo.contains("ComponentContext"));
        assertTrue("应该包含榜单上下文信息", allContextInfo.contains("RankingContext"));
        
        log.info("所有上下文信息: {}", allContextInfo);
        
        // 测试清除所有上下文
        ReportContext.clearAllContext();
        assertFalse("清除后不应该有任何上下文信息", ReportContext.hasAnyContext());
        assertNull("清除后优先actId应该为null", ReportContext.getPreferredActId());
        
        log.info("混合上下文测试完成");
    }
    
    @Test
    public void testThreadIsolation() {
        log.info("开始测试线程隔离功能");
        
        // 在主线程设置上下文
        ReportContext.setComponentContext(2025010405L, 777777L, 3L);
        assertEquals("主线程actId应该正确", Long.valueOf(2025010405L), ReportContext.getComponentActId());
        
        // 创建新线程测试隔离
        Thread testThread = new Thread(() -> {
            try {
                // 新线程应该有独立的上下文
                assertNull("新线程初始actId应该为null", ReportContext.getComponentActId());
                
                // 在新线程设置不同的上下文
                ReportContext.setComponentContext(2025010406L, 666666L, 4L);
                assertEquals("新线程actId应该正确", Long.valueOf(2025010406L), ReportContext.getComponentActId());
                
                log.info("新线程上下文设置成功");
                
            } catch (Exception e) {
                log.error("新线程测试失败", e);
            }
        });
        
        testThread.start();
        try {
            testThread.join();
        } catch (InterruptedException e) {
            log.error("等待线程结束失败", e);
        }
        
        // 主线程的上下文应该不受影响
        assertEquals("主线程actId应该保持不变", Long.valueOf(2025010405L), ReportContext.getComponentActId());
        
        log.info("线程隔离测试完成");
    }
    
    @Test
    public void testNullValues() {
        log.info("开始测试null值处理");
        
        // 测试设置null值
        ReportContext.setComponentContext(null, null, null);
        assertNull("null actId应该正确设置", ReportContext.getComponentActId());
        assertNull("null cmptId应该正确设置", ReportContext.getComponentId());
        assertNull("null cmptIndex应该正确设置", ReportContext.getComponentIndex());
        
        ReportContext.setRankingContext(null, null, null, null);
        assertNull("null actId应该正确设置", ReportContext.getRankingActId());
        assertNull("null rankId应该正确设置", ReportContext.getRankId());
        assertNull("null phaseId应该正确设置", ReportContext.getPhaseId());
        assertNull("null timeCode应该正确设置", ReportContext.getTimeCode());
        
        // 测试优先actId在都为null时的行为
        assertNull("都为null时优先actId应该为null", ReportContext.getPreferredActId());
        
        // 测试hasAnyContext在都为null时的行为
        assertFalse("都为null时不应该有任何上下文", ReportContext.hasAnyContext());
        
        log.info("null值处理测试完成");
    }
}
