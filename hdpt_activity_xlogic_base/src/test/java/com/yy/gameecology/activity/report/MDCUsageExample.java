package com.yy.gameecology.activity.report;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * MDC使用示例
 * 展示如何在多线程环境中使用ReportContext和MDC
 * 
 * <AUTHOR> Generated
 * @date 2025-01-04
 */
public class MDCUsageExample {
    
    private static final Logger log = LoggerFactory.getLogger(MDCUsageExample.class);
    
    /**
     * 示例1：基本MDC使用
     */
    public static void basicMDCUsage() {
        log.info("=== 基本MDC使用示例 ===");
        
        try {
            // 设置组件上下文
            ReportContext.setComponentContext(2025010401L, 888888L, 1L);
            
            // 设置榜单上下文
            ReportContext.setRankingContext(2025010402L, 1001L, 2001L, "20250104");
            
            log.info("设置上下文完成");
            log.info("组件上下文: {}", ReportContext.getComponentContext());
            log.info("榜单上下文: {}", ReportContext.getRankingContext());
            log.info("所有MDC信息: {}", ReportContext.getAllMDCInfo());
            
            // 模拟业务操作
            performBusinessOperation();
            
        } finally {
            // 清理上下文
            ReportContext.clearAllContext();
            log.info("上下文已清理");
        }
    }
    
    /**
     * 示例2：多线程MDC传递
     */
    public static void multiThreadMDCUsage() {
        log.info("=== 多线程MDC传递示例 ===");
        
        try {
            // 在主线程设置上下文
            ReportContext.setComponentContext(2025010403L, 999999L, 2L);
            log.info("主线程设置上下文: {}", ReportContext.getComponentContext());
            
            // 获取当前线程的MDC
            Map<String, String> parentMDC = MDC.getCopyOfContextMap();
            
            // 使用CompletableFuture创建异步任务
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 在子线程中设置父线程的MDC
                    if (parentMDC != null) {
                        MDC.setContextMap(parentMDC);
                    }
                    
                    // 验证子线程可以获取到父线程的上下文
                    ReportContext.ComponentContext context = ReportContext.getComponentContext();
                    log.info("子线程获取到的上下文: {}", context);
                    
                    // 子线程可以修改自己的上下文
                    ReportContext.setComponentContext(2025010404L, 777777L, 3L);
                    log.info("子线程修改后的上下文: {}", ReportContext.getComponentContext());
                    
                    // 模拟子线程业务操作
                    performAsyncBusinessOperation();
                    
                } catch (Exception e) {
                    log.error("子线程执行失败", e);
                } finally {
                    // 清理子线程的MDC
                    MDC.clear();
                    log.info("子线程MDC已清理");
                }
            });
            
            // 等待异步任务完成
            future.join();
            
            // 验证主线程的上下文没有被影响
            log.info("主线程上下文（应该保持不变）: {}", ReportContext.getComponentContext());
            
        } finally {
            ReportContext.clearAllContext();
        }
    }
    
    /**
     * 示例3：线程池中的MDC传递
     */
    public static void threadPoolMDCUsage() {
        log.info("=== 线程池MDC传递示例 ===");
        
        ExecutorService executor = Executors.newFixedThreadPool(3);
        
        try {
            // 在主线程设置上下文
            ReportContext.setComponentContext(2025010405L, 666666L, 4L);
            ReportContext.setRankingContext(2025010406L, 1002L, 2002L, "20250105");
            
            log.info("主线程设置上下文完成: {}", ReportContext.getAllContextInfo());
            
            // 获取当前线程的MDC
            Map<String, String> parentMDC = MDC.getCopyOfContextMap();
            
            // 提交多个任务到线程池
            for (int i = 1; i <= 3; i++) {
                final int taskId = i;
                executor.submit(() -> {
                    try {
                        // 在工作线程中设置父线程的MDC
                        if (parentMDC != null) {
                            MDC.setContextMap(parentMDC);
                        }
                        
                        log.info("工作线程{}获取到的上下文: {}", taskId, ReportContext.getAllContextInfo());
                        
                        // 每个工作线程可以有自己的上下文修改
                        ReportContext.setComponentContext(
                            2025010405L + taskId, 
                            666666L + taskId, 
                            4L + taskId
                        );
                        
                        log.info("工作线程{}修改后的上下文: {}", taskId, ReportContext.getComponentContext());
                        
                        // 模拟工作线程业务操作
                        performWorkerThreadOperation(taskId);
                        
                    } catch (Exception e) {
                        log.error("工作线程{}执行失败", taskId, e);
                    } finally {
                        // 清理工作线程的MDC
                        MDC.clear();
                        log.info("工作线程{}MDC已清理", taskId);
                    }
                });
            }
            
            // 等待所有任务完成
            Thread.sleep(2000);
            
            // 验证主线程的上下文没有被影响
            log.info("主线程上下文（应该保持不变）: {}", ReportContext.getAllContextInfo());
            
        } catch (InterruptedException e) {
            log.error("等待任务完成时被中断", e);
        } finally {
            executor.shutdown();
            ReportContext.clearAllContext();
        }
    }
    
    /**
     * 示例4：嵌套方法调用中的MDC使用
     */
    public static void nestedMethodMDCUsage() {
        log.info("=== 嵌套方法调用MDC示例 ===");
        
        try {
            // 外层方法设置上下文
            ReportContext.setComponentContext(2025010407L, 555555L, 5L);
            log.info("外层方法设置上下文: {}", ReportContext.getComponentContext());
            
            // 调用中层方法
            middleLayerMethod();
            
            // 验证外层方法的上下文
            log.info("外层方法上下文（调用后）: {}", ReportContext.getComponentContext());
            
        } finally {
            ReportContext.clearAllContext();
        }
    }
    
    /**
     * 中层方法
     */
    private static void middleLayerMethod() {
        log.info("中层方法开始，当前上下文: {}", ReportContext.getComponentContext());
        
        // 中层方法可以修改上下文
        ReportContext.setRankingContext(2025010408L, 1003L, 2003L, "20250106");
        log.info("中层方法设置榜单上下文: {}", ReportContext.getRankingContext());
        
        // 调用内层方法
        innerLayerMethod();
        
        log.info("中层方法结束，当前上下文: {}", ReportContext.getAllContextInfo());
    }
    
    /**
     * 内层方法
     */
    private static void innerLayerMethod() {
        log.info("内层方法开始，当前上下文: {}", ReportContext.getAllContextInfo());
        
        // 内层方法可以获取到外层和中层设置的所有上下文
        Long componentActId = ReportContext.getComponentActId();
        Long rankingActId = ReportContext.getRankingActId();
        Long cmptId = ReportContext.getComponentId();
        Long rankId = ReportContext.getRankId();
        
        log.info("内层方法获取到的信息: componentActId={}, rankingActId={}, cmptId={}, rankId={}", 
                componentActId, rankingActId, cmptId, rankId);
        
        // 内层方法也可以修改上下文
        ReportContext.setComponentContext(componentActId, cmptId, 99L);
        log.info("内层方法修改组件索引后: {}", ReportContext.getComponentContext());
    }
    
    /**
     * 模拟业务操作
     */
    private static void performBusinessOperation() {
        log.info("执行业务操作，当前上下文: {}", ReportContext.getAllContextInfo());
        // 模拟业务逻辑
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 模拟异步业务操作
     */
    private static void performAsyncBusinessOperation() {
        log.info("执行异步业务操作，当前上下文: {}", ReportContext.getAllContextInfo());
        // 模拟异步业务逻辑
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 模拟工作线程操作
     */
    private static void performWorkerThreadOperation(int taskId) {
        log.info("工作线程{}执行操作，当前上下文: {}", taskId, ReportContext.getAllContextInfo());
        // 模拟工作线程业务逻辑
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 运行所有示例
     */
    public static void runAllExamples() {
        log.info("开始运行MDC使用示例");
        
        try {
            basicMDCUsage();
            Thread.sleep(500);
            
            multiThreadMDCUsage();
            Thread.sleep(500);
            
            threadPoolMDCUsage();
            Thread.sleep(500);
            
            nestedMethodMDCUsage();
            
        } catch (InterruptedException e) {
            log.error("示例执行被中断", e);
        } finally {
            // 确保所有MDC都被清理
            MDC.clear();
            log.info("所有MDC使用示例执行完成");
        }
    }
    
    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        runAllExamples();
    }
}
