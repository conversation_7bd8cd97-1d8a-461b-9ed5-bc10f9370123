package com.yy.gameecology.activity.report;

import com.yy.gameecology.activity.bean.hdzt.BaseEvent;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeStart;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import org.junit.After;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import static org.junit.Assert.*;

/**
 * ReportContextResolver测试类
 * 测试从BaseEvent子类中提取参数的功能
 * 
 * <AUTHOR> Generated
 * @date 2025-01-04
 */
public class ReportContextResolverTest {
    
    private static final Logger log = LoggerFactory.getLogger(ReportContextResolverTest.class);
    
    @After
    public void tearDown() {
        // 每个测试后清理MDC
        ReportContext.clearAllContext();
        MDC.clear();
    }
    
    @Test
    public void testExtractFromPhaseTimeEnd() {
        log.info("开始测试从PhaseTimeEnd提取参数");
        
        // 创建PhaseTimeEnd事件
        PhaseTimeEnd event = new PhaseTimeEnd();
        event.setActId(2025010401L);
        event.setRankId(1001L);
        event.setPhaseId(2001L);
        event.setTimeKey(1L); // 按日分榜
        event.setEndTime("2025-01-04 23:59:59");
        
        // 测试参数提取
        Long actId = ReportContextResolver.extractActId(event);
        Long rankId = ReportContextResolver.extractRankId(event);
        Long phaseId = ReportContextResolver.extractPhaseId(event);
        String timeCode = ReportContextResolver.extractTimeCode(event);
        
        // 验证提取结果
        assertEquals("actId应该正确提取", Long.valueOf(2025010401L), actId);
        assertEquals("rankId应该正确提取", Long.valueOf(1001L), rankId);
        assertEquals("phaseId应该正确提取", Long.valueOf(2001L), phaseId);
        assertEquals("timeCode应该正确计算", "20250104", timeCode);
        
        log.info("PhaseTimeEnd参数提取结果: actId={}, rankId={}, phaseId={}, timeCode={}", 
                actId, rankId, phaseId, timeCode);
        
        // 测试设置上下文
        boolean success = ReportContextResolver.resolveAndSetRankingContext(event);
        assertTrue("应该成功设置榜单上下文", success);
        
        // 验证上下文设置
        ReportContext.RankingContext context = ReportContext.getRankingContext();
        assertEquals("上下文actId应该正确", Long.valueOf(2025010401L), context.getActId());
        assertEquals("上下文rankId应该正确", Long.valueOf(1001L), context.getRankId());
        assertEquals("上下文phaseId应该正确", Long.valueOf(2001L), context.getPhaseId());
        assertEquals("上下文timeCode应该正确", "20250104", context.getTimeCode());
        
        log.info("PhaseTimeEnd测试完成");
    }
    
    @Test
    public void testExtractFromPhaseTimeStart() {
        log.info("开始测试从PhaseTimeStart提取参数");
        
        // 创建PhaseTimeStart事件（没有endTime字段）
        PhaseTimeStart event = new PhaseTimeStart();
        event.setActId(2025010402L);
        event.setRankId(1002L);
        event.setPhaseId(2002L);
        event.setTimeKey(2L); // 按小时分榜
        event.setStartTime("2025-01-04 10:00:00");
        
        // 测试参数提取
        Long actId = ReportContextResolver.extractActId(event);
        Long rankId = ReportContextResolver.extractRankId(event);
        Long phaseId = ReportContextResolver.extractPhaseId(event);
        String timeCode = ReportContextResolver.extractTimeCode(event);
        
        // 验证提取结果
        assertEquals("actId应该正确提取", Long.valueOf(2025010402L), actId);
        assertEquals("rankId应该正确提取", Long.valueOf(1002L), rankId);
        assertEquals("phaseId应该正确提取", Long.valueOf(2002L), phaseId);
        assertNull("timeCode应该为null（因为没有endTime）", timeCode);
        
        log.info("PhaseTimeStart参数提取结果: actId={}, rankId={}, phaseId={}, timeCode={}", 
                actId, rankId, phaseId, timeCode);
        
        // 测试设置上下文
        boolean success = ReportContextResolver.resolveAndSetRankingContext(event);
        assertTrue("应该成功设置榜单上下文", success);
        
        // 验证上下文设置
        ReportContext.RankingContext context = ReportContext.getRankingContext();
        assertEquals("上下文actId应该正确", Long.valueOf(2025010402L), context.getActId());
        assertEquals("上下文rankId应该正确", Long.valueOf(1002L), context.getRankId());
        assertEquals("上下文phaseId应该正确", Long.valueOf(2002L), context.getPhaseId());
        assertNull("上下文timeCode应该为null", context.getTimeCode());
        
        log.info("PhaseTimeStart测试完成");
    }
    
    @Test
    public void testExtractFromPromotTimeEnd() {
        log.info("开始测试从PromotTimeEnd提取参数");
        
        // 创建PromotTimeEnd事件
        PromotTimeEnd event = new PromotTimeEnd();
        event.setActId(2025010403L);
        event.setRankId(1003L);
        event.setPhaseId(2003L);
        event.setTimeKey(2L); // 按小时分榜
        event.setEndTime("2025-01-04 15:59:59");
        
        // 测试参数提取
        Long actId = ReportContextResolver.extractActId(event);
        Long rankId = ReportContextResolver.extractRankId(event);
        Long phaseId = ReportContextResolver.extractPhaseId(event);
        String timeCode = ReportContextResolver.extractTimeCode(event);
        
        // 验证提取结果
        assertEquals("actId应该正确提取", Long.valueOf(2025010403L), actId);
        assertEquals("rankId应该正确提取", Long.valueOf(1003L), rankId);
        assertEquals("phaseId应该正确提取", Long.valueOf(2003L), phaseId);
        assertEquals("timeCode应该正确计算（按小时）", "2025010415", timeCode);
        
        log.info("PromotTimeEnd参数提取结果: actId={}, rankId={}, phaseId={}, timeCode={}", 
                actId, rankId, phaseId, timeCode);
        
        log.info("PromotTimeEnd测试完成");
    }
    
    @Test
    public void testExtractFromBaseEvent() {
        log.info("开始测试从BaseEvent基类提取参数");
        
        // 创建BaseEvent实例（没有phaseId、timeKey、endTime字段）
        BaseEvent event = new BaseEvent(BaseEvent.ACTIVITY_TIME_START);
        event.setActId(2025010404L);
        event.setRankId(1004L);
        
        // 测试参数提取
        Long actId = ReportContextResolver.extractActId(event);
        Long rankId = ReportContextResolver.extractRankId(event);
        Long phaseId = ReportContextResolver.extractPhaseId(event);
        String timeCode = ReportContextResolver.extractTimeCode(event);
        
        // 验证提取结果
        assertEquals("actId应该正确提取", Long.valueOf(2025010404L), actId);
        assertEquals("rankId应该正确提取", Long.valueOf(1004L), rankId);
        assertEquals("phaseId应该为0（基类没有此字段）", Long.valueOf(0L), phaseId);
        assertNull("timeCode应该为null（基类没有相关字段）", timeCode);
        
        log.info("BaseEvent参数提取结果: actId={}, rankId={}, phaseId={}, timeCode={}", 
                actId, rankId, phaseId, timeCode);
        
        log.info("BaseEvent测试完成");
    }
    
    @Test
    public void testFieldDetection() {
        log.info("开始测试字段检测功能");
        
        PhaseTimeEnd phaseTimeEnd = new PhaseTimeEnd();
        PhaseTimeStart phaseTimeStart = new PhaseTimeStart();
        BaseEvent baseEvent = new BaseEvent(BaseEvent.ACTIVITY_TIME_START);
        
        // 测试PhaseTimeEnd的字段检测
        assertTrue("PhaseTimeEnd应该有phaseId字段", ReportContextResolver.hasField(phaseTimeEnd, "phaseId"));
        assertTrue("PhaseTimeEnd应该有timeKey字段", ReportContextResolver.hasField(phaseTimeEnd, "timeKey"));
        assertTrue("PhaseTimeEnd应该有endTime字段", ReportContextResolver.hasField(phaseTimeEnd, "endTime"));
        assertFalse("PhaseTimeEnd不应该有startTime字段", ReportContextResolver.hasField(phaseTimeEnd, "startTime"));
        
        // 测试PhaseTimeStart的字段检测
        assertTrue("PhaseTimeStart应该有phaseId字段", ReportContextResolver.hasField(phaseTimeStart, "phaseId"));
        assertTrue("PhaseTimeStart应该有timeKey字段", ReportContextResolver.hasField(phaseTimeStart, "timeKey"));
        assertTrue("PhaseTimeStart应该有startTime字段", ReportContextResolver.hasField(phaseTimeStart, "startTime"));
        assertFalse("PhaseTimeStart不应该有endTime字段", ReportContextResolver.hasField(phaseTimeStart, "endTime"));
        
        // 测试BaseEvent的字段检测
        assertFalse("BaseEvent不应该有phaseId字段", ReportContextResolver.hasField(baseEvent, "phaseId"));
        assertFalse("BaseEvent不应该有timeKey字段", ReportContextResolver.hasField(baseEvent, "timeKey"));
        assertFalse("BaseEvent不应该有endTime字段", ReportContextResolver.hasField(baseEvent, "endTime"));
        
        log.info("字段检测测试完成");
    }
    
    @Test
    public void testValidation() {
        log.info("开始测试事件验证功能");
        
        // 测试有效事件
        PhaseTimeEnd validEvent = new PhaseTimeEnd();
        validEvent.setActId(2025010405L);
        validEvent.setRankId(1005L);
        validEvent.setPhaseId(2005L);
        validEvent.setTimeKey(1L);
        validEvent.setEndTime("2025-01-05 23:59:59");
        
        ReportContextResolver.ValidationResult validResult = ReportContextResolver.validateEvent(validEvent);
        assertTrue("有效事件应该通过验证", validResult.isValid());
        log.info("有效事件验证结果: {}", validResult);
        
        // 测试无效事件（缺少actId）
        BaseEvent invalidEvent = new BaseEvent(BaseEvent.ACTIVITY_TIME_START);
        invalidEvent.setActId(0L); // 无效的actId
        invalidEvent.setRankId(1006L);
        
        ReportContextResolver.ValidationResult invalidResult = ReportContextResolver.validateEvent(invalidEvent);
        assertFalse("无效事件不应该通过验证", invalidResult.isValid());
        assertTrue("验证消息应该包含actId问题", invalidResult.getMessage().contains("actId"));
        log.info("无效事件验证结果: {}", invalidResult);
        
        // 测试null事件
        ReportContextResolver.ValidationResult nullResult = ReportContextResolver.validateEvent(null);
        assertFalse("null事件不应该通过验证", nullResult.isValid());
        log.info("null事件验证结果: {}", nullResult);
        
        log.info("事件验证测试完成");
    }
    
    @Test
    public void testFieldSummary() {
        log.info("开始测试字段摘要功能");
        
        PhaseTimeEnd event = new PhaseTimeEnd();
        event.setActId(2025010406L);
        event.setRankId(1006L);
        event.setPhaseId(2006L);
        event.setTimeKey(1L);
        event.setEndTime("2025-01-06 23:59:59");
        
        String summary = ReportContextResolver.getFieldSummary(event);
        assertNotNull("摘要不应该为null", summary);
        assertTrue("摘要应该包含类名", summary.contains("PhaseTimeEnd"));
        assertTrue("摘要应该包含actId", summary.contains("actId=2025010406"));
        assertTrue("摘要应该包含rankId", summary.contains("rankId=1006"));
        assertTrue("摘要应该包含phaseId", summary.contains("phaseId=2006"));
        assertTrue("摘要应该包含timeCode", summary.contains("timeCode=20250106"));
        
        log.info("字段摘要: {}", summary);
        log.info("字段摘要测试完成");
    }
    
    @Test
    public void testBatchProcessing() {
        log.info("开始测试批量处理功能");
        
        PhaseTimeEnd event1 = new PhaseTimeEnd();
        event1.setActId(2025010407L);
        event1.setRankId(1007L);
        event1.setPhaseId(2007L);
        
        PhaseTimeStart event2 = new PhaseTimeStart();
        event2.setActId(2025010408L);
        event2.setRankId(1008L);
        event2.setPhaseId(2008L);
        
        BaseEvent event3 = new BaseEvent(BaseEvent.ACTIVITY_TIME_START);
        event3.setActId(2025010409L);
        event3.setRankId(1009L);
        
        int successCount = ReportContextResolver.batchResolveAndSetRankingContext(event1, event2, event3);
        assertEquals("应该成功处理3个事件", 3, successCount);
        
        log.info("批量处理测试完成，成功处理{}个事件", successCount);
    }
}
