package com.yy.gameecology.activity.report;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.hd.qly.client.ReporterClient;
import com.yy.protocol.pb.GameecologyActivity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Component;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 增强的服务运维数据上报切面测试类
 * 测试对SvcSDKService、CommonBroadCastService、KafkaService、HdztAwardServiceClient的拦截
 * 
 * <AUTHOR> Generated
 * @date 2025-01-04
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(locations = "classpath:env/dev/application.properties")
public class EnhancedServiceReportAspectTest {
    
    private static final Logger log = LoggerFactory.getLogger(EnhancedServiceReportAspectTest.class);
    
    @Autowired(required = false)
    private SvcSDKService svcSDKService;
    
    @Autowired(required = false)
    private CommonBroadCastService commonBroadCastService;
    
    @Autowired(required = false)
    private KafkaService kafkaService;
    
    @Autowired(required = false)
    private HdztAwardServiceClient hdztAwardServiceClient;
    
    @Autowired(required = false)
    private ReporterClient reporterClient;
    
    @Autowired(required = false)
    private TestComponent testComponent;
    
    /**
     * 测试组件类，继承BaseActComponent
     */
    @Component
    public static class TestComponent extends BaseActComponent<TestComponentAttr> {
        
        @Autowired(required = false)
        private SvcSDKService svcSDKService;
        
        @Autowired(required = false)
        private CommonBroadCastService commonBroadCastService;
        
        @Autowired(required = false)
        private KafkaService kafkaService;
        
        @Autowired(required = false)
        private HdztAwardServiceClient hdztAwardServiceClient;
        
        @Override
        public Long getComponentId() {
            return 888888L; // 测试用的组件ID
        }
        
        /**
         * 测试SvcSDKService调用
         */
        public void testSvcSDKServiceCall() {
            if (svcSDKService != null) {
                try {
                    // 设置上下文
                    ReportContext.setComponentContext(2025010401L, getComponentId(), 1L);
                    
                    GameecologyActivity.GameEcologyMsg.Builder builder = 
                        GameecologyActivity.GameEcologyMsg.newBuilder();
                    builder.setUri(12345);
//                    builder.setco("SvcSDKService test data");
                    builder.setSeq(98765L);
                    GameecologyActivity.GameEcologyMsg message = builder.build();
                    
                    svcSDKService.unicastUid(1001L, message);
                    log.info("SvcSDKService调用完成");
                } finally {
                    ReportContext.clearComponentContext();
                }
            } else {
                log.warn("SvcSDKService未注入，跳过测试");
            }
        }
        
        /**
         * 测试CommonBroadCastService调用
         */
        public void testCommonBroadCastServiceCall() {
            if (commonBroadCastService != null) {
                try {
                    // 设置上下文
                    ReportContext.setComponentContext(2025010402L, getComponentId(), 2L);
                    
                    // 调用通用通知单播方法
                    commonBroadCastService.commonNoticeUnicast(
                        2025010402L, 
                        "testNotice", 
                        "测试通知", 
                        "{\"test\": \"data\"}", 
                        1002L
                    );
                    log.info("CommonBroadCastService调用完成");
                } finally {
                    ReportContext.clearComponentContext();
                }
            } else {
                log.warn("CommonBroadCastService未注入，跳过测试");
            }
        }
        
        /**
         * 测试KafkaService调用
         */
        public void testKafkaServiceCall() {
            if (kafkaService != null) {
                try {
                    // 设置上下文
                    ReportContext.setComponentContext(2025010403L, getComponentId(), 3L);
                    
                    // 创建测试事件（这里使用一个简单的测试，实际可能需要具体的事件对象）
                    // 由于KafkaService的方法需要特定的事件对象，这里只是演示
                    log.info("模拟KafkaService调用");
                    // kafkaService.sendJiaoyouKafka("test_topic", "test message");
                    
                    log.info("KafkaService调用完成");
                } finally {
                    ReportContext.clearComponentContext();
                }
            } else {
                log.warn("KafkaService未注入，跳过测试");
            }
        }
        
        /**
         * 测试HdztAwardServiceClient调用
         */
        public void testHdztAwardServiceClientCall() {
            if (hdztAwardServiceClient != null) {
                try {
                    // 设置上下文
                    ReportContext.setComponentContext(2025010404L, getComponentId(), 4L);
                    
                    // 调用查询方法（只读操作，相对安全）
                    try {
                        hdztAwardServiceClient.queryAwardTasks(12345L);
                        log.info("HdztAwardServiceClient调用完成");
                    } catch (Exception e) {
                        log.info("HdztAwardServiceClient调用异常（这是正常的，因为可能没有对应的数据）: {}", e.getMessage());
                    }
                } finally {
                    ReportContext.clearComponentContext();
                }
            } else {
                log.warn("HdztAwardServiceClient未注入，跳过测试");
            }
        }
        
        /**
         * 测试混合服务调用
         */
        public void testMixedServiceCalls() {
            try {
                // 设置上下文
                ReportContext.setComponentContext(2025010405L, getComponentId(), 5L);
                
                log.info("开始混合服务调用测试");
                
                // 依次调用不同的服务
                testSvcSDKServiceCall();
                Thread.sleep(100);
                
                testCommonBroadCastServiceCall();
                Thread.sleep(100);
                
                testKafkaServiceCall();
                Thread.sleep(100);
                
                testHdztAwardServiceClientCall();
                
                log.info("混合服务调用测试完成");
                
            } catch (InterruptedException e) {
                log.error("混合服务调用测试被中断", e);
            } finally {
                ReportContext.clearComponentContext();
            }
        }
    }
    
    /**
     * 测试组件属性类
     */
    public static class TestComponentAttr extends ComponentAttr {
        // 测试用的组件属性类
    }
    
    @Test
    public void testSvcSDKServiceInterception() {
        log.info("开始测试SvcSDKService拦截功能");
        
        if (testComponent == null) {
            log.warn("TestComponent未注入，无法进行测试");
            return;
        }
        
        try {
            testComponent.testSvcSDKServiceCall();
            Thread.sleep(1000); // 等待异步上报完成
            log.info("SvcSDKService拦截测试完成");
        } catch (Exception e) {
            log.error("SvcSDKService拦截测试失败", e);
        }
    }
    
    @Test
    public void testCommonBroadCastServiceInterception() {
        log.info("开始测试CommonBroadCastService拦截功能");
        
        if (testComponent == null) {
            log.warn("TestComponent未注入，无法进行测试");
            return;
        }
        
        try {
            testComponent.testCommonBroadCastServiceCall();
            Thread.sleep(1000); // 等待异步上报完成
            log.info("CommonBroadCastService拦截测试完成");
        } catch (Exception e) {
            log.error("CommonBroadCastService拦截测试失败", e);
        }
    }
    
    @Test
    public void testKafkaServiceInterception() {
        log.info("开始测试KafkaService拦截功能");
        
        if (testComponent == null) {
            log.warn("TestComponent未注入，无法进行测试");
            return;
        }
        
        try {
            testComponent.testKafkaServiceCall();
            Thread.sleep(1000); // 等待异步上报完成
            log.info("KafkaService拦截测试完成");
        } catch (Exception e) {
            log.error("KafkaService拦截测试失败", e);
        }
    }
    
    @Test
    public void testHdztAwardServiceClientInterception() {
        log.info("开始测试HdztAwardServiceClient拦截功能");
        
        if (testComponent == null) {
            log.warn("TestComponent未注入，无法进行测试");
            return;
        }
        
        try {
            testComponent.testHdztAwardServiceClientCall();
            Thread.sleep(1000); // 等待异步上报完成
            log.info("HdztAwardServiceClient拦截测试完成");
        } catch (Exception e) {
            log.error("HdztAwardServiceClient拦截测试失败", e);
        }
    }
    
    @Test
    public void testAllServicesInterception() {
        log.info("开始测试所有服务拦截功能");
        
        if (testComponent == null) {
            log.warn("TestComponent未注入，无法进行测试");
            return;
        }
        
        try {
            testComponent.testMixedServiceCalls();
            Thread.sleep(2000); // 等待所有异步上报完成
            log.info("所有服务拦截测试完成");
        } catch (Exception e) {
            log.error("所有服务拦截测试失败", e);
        }
    }
    
    @Test
    public void testDirectServiceCall() {
        log.info("开始测试直接服务调用（不通过BaseActComponent）");
        
        if (svcSDKService == null) {
            log.warn("SvcSDKService未注入，无法进行测试");
            return;
        }
        
        try {
            // 直接调用服务，不通过BaseActComponent
            GameecologyActivity.GameEcologyMsg.Builder builder = 
                GameecologyActivity.GameEcologyMsg.newBuilder();
            builder.setUri(99999);
//            builder.setData("direct call test data");
            GameecologyActivity.GameEcologyMsg message = builder.build();
            
            svcSDKService.unicastUid(9999L, message);
            log.info("直接服务调用完成，这不应该触发运维数据上报");
            
            Thread.sleep(1000);
            
        } catch (Exception e) {
            log.error("直接服务调用测试失败", e);
        }
    }
}
