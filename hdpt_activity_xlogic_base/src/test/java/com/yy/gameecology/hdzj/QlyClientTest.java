package com.yy.gameecology.hdzj;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.hdzj.element.component.AwardUnicastComponent;
import com.yy.hd.qly.client.ReporterClient;
import com.yy.hd.qly.client.model.ActReportData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.CompletableFuture;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-08-04 19:44
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-6.properties", "classpath:env/local/application-inner.properties"})

public class QlyClientTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "1");
    }

    @Autowired(required = false)
    private ReporterClient reporterClient;

    @Autowired
    private AwardUnicastComponent awardUnicastComponent;

    @Test
    public void reportTest(){
        // 准备测试数据
        ActReportData actReportData = ReporterClient.createManualReport(
                "hdpt", "test", "集成测试", "integrationTest", "测试sendAsync集成"
        );
        actReportData.setActId(12345L);
        actReportData.setUid(1001L);
        actReportData.setTraceId("hdzk_trace_001");

        // 执行异步发送
        CompletableFuture<Boolean> future = reporterClient.sendAsync(actReportData);
    }

    @Test
    public void awardUnicastComponentTest(){
        awardUnicastComponent.unicastTip(2025071001L,123456,"testtips",1);
    }

}
