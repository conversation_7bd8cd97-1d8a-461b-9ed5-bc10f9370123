package com.yy.gameecology.y2025082001;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.BusiId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * desc:这是1个定制代码组件例子
 *
 * <AUTHOR>
 * @date 2023-03-13 14:52
 **/
@RequestMapping("/act2025082001")
@RestController
@Component
public class Act2025082001Component extends BaseActComponent<Act2025082001ComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SvcSDKService svcSDKService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Override
    public Long getComponentId() {
        return C2025082001.ACT_ID;
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChangeEvent(RankingScoreChanged event, Act2025082001ComponentAttr attr) {
        //监听辅助榜分值改变事件
        long rankId = event.getRankId();
        if (rankId != attr.getRankId()) {
            return;
        }

        long sid = 1454054224L;
        long ssid = 1454054224L;

        //最多10s上报1次测试数据，以免影响测试环境
        String key = makeKey(attr, "limit");
        boolean limit = actRedisDao.setNX(getRedisGroupCode(event.getActId()), key, System.currentTimeMillis() + "", 10);
        if (!limit) {
            return;
        }

        long uid = Convert.toLong(event.getMember());
        unicastTip(event.getActId(), uid, "单播拦截测试", 1);


        commonBroadCastService
                .commonBannerBroadcast(sid, ssid, 0L, Template.SkillCard, BroadcastType.SUB_CHANNEL
                        , event.getActId(), uid, 1L, 12345L, 456789L, ImmutableMap.of("content", "广播测试"));


        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), event.getSeq(),
                BroadCastHelpService.toAppBroBusiness(1), FstAppBroadcastType.SUB_CHANNEL, sid, ssid, "", Lists.newArrayList());
        final int mp4ContentType = 5;
        appBannerEvent.setContentType(mp4ContentType);
        appBannerEvent.setUidList(Lists.newArrayList(uid));
        kafkaService.sendAppBannerKafka(appBannerEvent);

        Map<Long, Integer> packageTmps = new HashMap<>();
        packageTmps.put(attr.getPackageId(), 1);
        Map<String, String> extData = ImmutableMap.of("desc", "发奖测试");
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.MAKE_FRIEND.getValue(), uid, attr.getTaskId(), ImmutableMap.of(attr.getPackageId(), packageTmps), event.getSeq(), extData);

    }

    public void unicastTip(long actId, long uid, String content, int type) {
        GameecologyActivity.Act202008_LotteryTips.Builder tips = GameecologyActivity.Act202008_LotteryTips.newBuilder()
                .setActId(actId).setType(type).setContent(content);
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202008_LotteryTips_VALUE)
                .setAct202008LotteryTips(tips).build();
        svcSDKService.unicastUid(uid, msg);
        log.info("unicastAward unicastTip  uid:{} type:{} content:{}", uid, type, content);
    }


}
