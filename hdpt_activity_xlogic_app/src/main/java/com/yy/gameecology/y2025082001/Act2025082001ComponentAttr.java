package com.yy.gameecology.y2025082001;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-03-13 14:53
 **/
@Data
public class Act2025082001ComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "响应事件的榜单id")
    private Long rankId = 31L;

    @ComponentAttrField(labelText = "发奖测试奖池id")
    private Long taskId;

    @ComponentAttrField(labelText = "发奖测试奖包id")
    private Long packageId;
}
